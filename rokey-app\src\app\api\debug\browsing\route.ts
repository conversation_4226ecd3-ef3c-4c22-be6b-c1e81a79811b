import { NextRequest, NextResponse } from 'next/server';
import BrowserlessService from '@/lib/browserless';

export async function POST(request: NextRequest) {
  try {
    const { query, searchEngine = 'google' } = await request.json();

    if (!query) {
      return NextResponse.json(
        { error: 'Query is required' },
        { status: 400 }
      );
    }

    console.log(`[Debug Browsing] Testing search for: "${query}"`);

    const browserless = BrowserlessService.getInstance();
    const result = await browserless.searchAndExtract(query, searchEngine);

    console.log(`[Debug Browsing] Search result:`, JSON.stringify(result, null, 2));

    return NextResponse.json({
      success: true,
      query,
      searchEngine,
      result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Debug Browsing] Error:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Browsing test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const browserless = BrowserlessService.getInstance();
    const stats = browserless.getStats();

    return NextResponse.json({
      status: 'operational',
      service: 'debug-browsing',
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('[Debug Browsing] Status check error:', error);
    
    return NextResponse.json(
      { 
        status: 'error',
        service: 'debug-browsing',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
