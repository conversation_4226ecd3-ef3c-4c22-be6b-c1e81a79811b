"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_browsing_BrowsingExecutionService_ts";
exports.ids = ["_rsc_src_lib_browsing_BrowsingExecutionService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/browserless.ts":
/*!********************************!*\
  !*** ./src/lib/browserless.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browserless.io API service with key rotation\n// Similar to Jina API key rotation system\nclass BrowserlessService {\n    constructor(){\n        this.apiKeys = [];\n        this.currentKeyIndex = 0;\n        this.keyUsageCount = new Map();\n        this.keyErrors = new Map();\n        this.MAX_RETRIES = 3;\n        this.ERROR_THRESHOLD = 5;\n        this.ENDPOINT = 'https://production-sfo.browserless.io';\n        this.initializeKeys();\n        this.testConnectivity();\n    }\n    static getInstance() {\n        if (!BrowserlessService.instance) {\n            BrowserlessService.instance = new BrowserlessService();\n        }\n        return BrowserlessService.instance;\n    }\n    initializeKeys() {\n        // Load all Browserless API keys from environment (similar to Jina pattern)\n        this.apiKeys = [\n            process.env.BROWSERLESS_API_KEY,\n            process.env.BROWSERLESS_API_KEY_2,\n            process.env.BROWSERLESS_API_KEY_3,\n            process.env.BROWSERLESS_API_KEY_4,\n            process.env.BROWSERLESS_API_KEY_5,\n            process.env.BROWSERLESS_API_KEY_6,\n            process.env.BROWSERLESS_API_KEY_7,\n            process.env.BROWSERLESS_API_KEY_8,\n            process.env.BROWSERLESS_API_KEY_9,\n            process.env.BROWSERLESS_API_KEY_10\n        ].filter(Boolean);\n        if (this.apiKeys.length === 0) {\n            console.error('No Browserless API keys found in environment variables');\n            return;\n        }\n        console.log(`[Browserless] Initialized with ${this.apiKeys.length} API keys`);\n        // Initialize usage tracking\n        this.apiKeys.forEach((key)=>{\n            this.keyUsageCount.set(key, 0);\n            this.keyErrors.set(key, 0);\n        });\n    }\n    /**\n   * Test connectivity to Browserless service\n   */ async testConnectivity() {\n        if (this.apiKeys.length === 0) {\n            console.warn('[Browserless] No API keys available for connectivity test');\n            return;\n        }\n        try {\n            const testKey = this.apiKeys[0];\n            const testUrl = `${this.ENDPOINT}/function?token=${testKey}`;\n            // Simple connectivity test with proper function format\n            const testCode = `\n        export default async function ({ page }) {\n          return {\n            status: \"connectivity-test-success\",\n            timestamp: new Date().toISOString()\n          };\n        }\n      `;\n            const response = await fetch(testUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/javascript'\n                },\n                body: testCode,\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const result = await response.text();\n                console.log('[Browserless] ✅ Connectivity test successful:', result);\n            } else {\n                const errorText = await response.text();\n                console.warn(`[Browserless] ⚠️ Connectivity test failed with status: ${response.status}`);\n                console.warn(`[Browserless] Error details: ${errorText}`);\n                console.warn(`[Browserless] Using key: ${testKey.substring(0, 8)}...`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.warn(`[Browserless] ⚠️ Connectivity test failed: ${errorMessage}`);\n            // Check for specific network errors\n            if (errorMessage.includes('ENOTFOUND')) {\n                console.error('[Browserless] 🌐 DNS resolution failed - check if chrome.browserless.io is accessible');\n            } else if (errorMessage.includes('ECONNRESET')) {\n                console.error('[Browserless] 🔌 Connection reset - possible network or service issue');\n            }\n        }\n    }\n    getNextApiKey() {\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Browserless API keys available');\n        }\n        // Find the key with lowest usage and errors\n        let bestKey = this.apiKeys[0];\n        let bestScore = this.calculateKeyScore(bestKey);\n        for (const key of this.apiKeys){\n            const score = this.calculateKeyScore(key);\n            if (score < bestScore) {\n                bestKey = key;\n                bestScore = score;\n            }\n        }\n        return bestKey;\n    }\n    calculateKeyScore(key) {\n        const usage = this.keyUsageCount.get(key) || 0;\n        const errors = this.keyErrors.get(key) || 0;\n        // Higher score = worse key (more usage + more errors)\n        return usage + errors * 10;\n    }\n    incrementKeyUsage(key) {\n        const currentUsage = this.keyUsageCount.get(key) || 0;\n        this.keyUsageCount.set(key, currentUsage + 1);\n    }\n    incrementKeyError(key) {\n        const currentErrors = this.keyErrors.get(key) || 0;\n        this.keyErrors.set(key, currentErrors + 1);\n    }\n    isKeyHealthy(key) {\n        const errors = this.keyErrors.get(key) || 0;\n        return errors < this.ERROR_THRESHOLD;\n    }\n    getHealthyKeys() {\n        return this.apiKeys.filter((key)=>this.isKeyHealthy(key));\n    }\n    async executeFunction(code, context, config) {\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            // Reset error counts if all keys are unhealthy\n            this.keyErrors.clear();\n            this.apiKeys.forEach((key)=>this.keyErrors.set(key, 0));\n            console.log('All Browserless keys were unhealthy, resetting error counts');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const response = await this.makeRequest(apiKey, code, context, config);\n                // Success - return the response\n                return response;\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless attempt ${attempt + 1} failed:`, error);\n                // If it's a rate limit or quota error, mark the key as having an error\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless API attempts failed');\n    }\n    async makeRequest(apiKey, code, context, config) {\n        const url = `${this.ENDPOINT}/function?token=${apiKey}`;\n        const requestBody = context ? {\n            code,\n            context\n        } : code;\n        const headers = {\n            'Content-Type': context ? 'application/json' : 'application/javascript',\n            'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'\n        };\n        const response = await fetch(url, {\n            method: 'POST',\n            headers,\n            body: context ? JSON.stringify(requestBody) : code,\n            signal: AbortSignal.timeout(config?.timeout || 30000)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Browserless API error: ${response.status} - ${errorText}`);\n        }\n        const result = await response.json();\n        return result;\n    }\n    isRateLimitError(error) {\n        const message = error.message.toLowerCase();\n        return message.includes('rate limit') || message.includes('quota') || message.includes('429') || message.includes('too many requests');\n    }\n    // Convenience methods for common browser tasks\n    async navigateAndExtract(url, selector) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n        \n        const title = await page.title();\n        const content = ${selector ? `await page.$eval(\"${selector}\", el => el.textContent || el.innerText)` : 'await page.evaluate(() => document.body.innerText)'};\n        \n        return {\n          data: {\n            url: \"${url}\",\n            title,\n            content: content?.trim() || \"\"\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async searchAndExtract(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${searchUrl}\", { waitUntil: 'networkidle0' });\n\n        // Wait for search results to load with multiple fallback selectors\n        let resultsLoaded = false;\n        const googleSelectors = ['[data-ved]', 'h3', '.g h3', '.LC20lb', '.DKV0Md', '#search h3'];\n        const bingSelectors = ['.b_algo', '.b_algo h2', 'h2 a'];\n\n        const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n\n        for (const selector of selectorsToTry) {\n          try {\n            await page.waitForSelector(selector, { timeout: 3000 });\n            resultsLoaded = true;\n            console.log('Found results with selector:', selector);\n            break;\n          } catch (e) {\n            console.log('Selector failed:', selector);\n            continue;\n          }\n        }\n\n        if (!resultsLoaded) {\n          // Give it one more chance with a longer timeout on the most common selector\n          try {\n            await page.waitForSelector('${searchEngine === 'google' ? 'h3' : '.b_algo'}', { timeout: 5000 });\n          } catch (e) {\n            console.log('All selectors failed, proceeding anyway...');\n          }\n        }\n\n        const results = await page.evaluate(() => {\n          console.log('Starting search results extraction...');\n\n          // Try multiple selectors for extracting results\n          const googleSelectors = [\n            '[data-ved] h3',\n            'h3',\n            '.g h3',\n            '.LC20lb',\n            '.DKV0Md',\n            '#search h3',\n            '.yuRUbf h3',\n            'a h3',\n            '[role=\"heading\"]'\n          ];\n          const bingSelectors = ['.b_algo h2', '.b_algo h2 a', 'h2 a'];\n\n          const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n          let elements = [];\n          let usedSelector = '';\n\n          // Debug: Log page content\n          console.log('Page title:', document.title);\n          console.log('Page URL:', window.location.href);\n\n          for (const selector of selectorsToTry) {\n            elements = document.querySelectorAll(selector);\n            console.log('Trying selector:', selector, 'found:', elements.length, 'elements');\n            if (elements.length > 0) {\n              usedSelector = selector;\n              console.log('Found', elements.length, 'results with selector:', selector);\n              break;\n            }\n          }\n\n          // If no results found, try a more generic approach\n          if (elements.length === 0) {\n            console.log('No results with specific selectors, trying generic approach...');\n            // Try to find any links that look like search results\n            const allLinks = document.querySelectorAll('a[href*=\"/url?\"]');\n            console.log('Found', allLinks.length, 'Google result links');\n\n            if (allLinks.length > 0) {\n              elements = Array.from(allLinks).map(link => {\n                const h3 = link.querySelector('h3');\n                return h3 || link;\n              }).filter(el => el && el.textContent?.trim());\n              usedSelector = 'a[href*=\"/url?\"] h3 (fallback)';\n              console.log('Using fallback approach, found', elements.length, 'elements');\n            }\n          }\n\n          const extractedResults = Array.from(elements).slice(0, 5).map(el => {\n            const title = el.textContent?.trim() || '';\n            let link = '';\n\n            // Try to get the link\n            if (el.href) {\n              link = el.href;\n            } else {\n              const closestLink = el.closest('a');\n              if (closestLink) {\n                link = closestLink.href;\n              }\n            }\n\n            return { title, link };\n          }).filter(item => item.title && item.link);\n\n          console.log('Final results:', extractedResults.length, 'items using selector:', usedSelector);\n          return extractedResults;\n        });\n\n        return {\n          data: {\n            query: \"${query}\",\n            searchEngine: \"${searchEngine}\",\n            results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async takeScreenshot(url, options) {\n        const fullPage = options?.fullPage ?? false;\n        const selector = options?.selector || '';\n        const quality = options?.quality || 80;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        let screenshot;\n        if (\"${selector}\") {\n          // Screenshot specific element\n          const element = await page.waitForSelector(\"${selector}\", { timeout: 10000 });\n          screenshot = await element.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n        } else {\n          // Screenshot full page or viewport\n          screenshot = await page.screenshot({\n            encoding: 'base64',\n            fullPage: ${fullPage},\n            type: 'png',\n            quality: ${quality}\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            screenshot: screenshot,\n            selector: \"${selector}\",\n            fullPage: ${fullPage},\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced form filling with intelligent field detection\n   */ async fillForm(url, formData, options) {\n        const submitAfterFill = options?.submitAfterFill ?? false;\n        const waitForNavigation = options?.waitForNavigation ?? false;\n        const formSelector = options?.formSelector || 'form';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const formData = ${JSON.stringify(formData)};\n        const results = [];\n\n        // Wait for form to be present\n        await page.waitForSelector(\"${formSelector}\", { timeout: 10000 });\n\n        // Fill each field intelligently\n        for (const [fieldName, value] of Object.entries(formData)) {\n          try {\n            // Try multiple selector strategies\n            const selectors = [\n              \\`input[name=\"\\${fieldName}\"]\\`,\n              \\`input[id=\"\\${fieldName}\"]\\`,\n              \\`textarea[name=\"\\${fieldName}\"]\\`,\n              \\`select[name=\"\\${fieldName}\"]\\`,\n              \\`input[placeholder*=\"\\${fieldName}\"]\\`,\n              \\`input[aria-label*=\"\\${fieldName}\"]\\`,\n              \\`[data-testid=\"\\${fieldName}\"]\\`\n            ];\n\n            let filled = false;\n            for (const selector of selectors) {\n              const elements = await page.$$(selector);\n              if (elements.length > 0) {\n                const element = elements[0];\n                const tagName = await element.evaluate(el => el.tagName.toLowerCase());\n\n                if (tagName === 'select') {\n                  await element.selectOption(value.toString());\n                } else if (tagName === 'input') {\n                  const inputType = await element.getAttribute('type');\n                  if (inputType === 'checkbox' || inputType === 'radio') {\n                    if (value) await element.check();\n                  } else {\n                    await element.fill(value.toString());\n                  }\n                } else {\n                  await element.fill(value.toString());\n                }\n\n                results.push({\n                  field: fieldName,\n                  selector: selector,\n                  value: value,\n                  success: true\n                });\n                filled = true;\n                break;\n              }\n            }\n\n            if (!filled) {\n              results.push({\n                field: fieldName,\n                value: value,\n                success: false,\n                error: 'Field not found'\n              });\n            }\n          } catch (error) {\n            results.push({\n              field: fieldName,\n              value: value,\n              success: false,\n              error: error.message\n            });\n          }\n        }\n\n        let submitResult = null;\n        if (${submitAfterFill}) {\n          try {\n            const submitButton = await page.$('input[type=\"submit\"], button[type=\"submit\"], button:has-text(\"Submit\")');\n            if (submitButton) {\n              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}\n              submitResult = { success: true, message: 'Form submitted successfully' };\n            } else {\n              submitResult = { success: false, error: 'Submit button not found' };\n            }\n          } catch (error) {\n            submitResult = { success: false, error: error.message };\n          }\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            formFillResults: results,\n            submitResult: submitResult,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * CAPTCHA solving with multiple strategies\n   */ async solveCaptcha(url, captchaType = 'recaptcha') {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const captchaType = \"${captchaType}\";\n        let result = { success: false, type: captchaType };\n\n        try {\n          if (captchaType === 'recaptcha') {\n            // Look for reCAPTCHA\n            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');\n            if (recaptcha) {\n              // For now, we'll detect and report the presence\n              // In production, integrate with 2captcha or similar service\n              const sitekey = await recaptcha.getAttribute('data-sitekey');\n              result = {\n                success: false,\n                type: 'recaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'reCAPTCHA detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'hcaptcha') {\n            // Look for hCaptcha\n            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');\n            if (hcaptcha) {\n              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');\n              result = {\n                success: false,\n                type: 'hcaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'hCaptcha detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'text') {\n            // Look for text-based CAPTCHA\n            const textCaptcha = await page.$('img[src*=\"captcha\"], img[alt*=\"captcha\"], .captcha-image');\n            if (textCaptcha) {\n              result = {\n                success: false,\n                type: 'text',\n                detected: true,\n                message: 'Text CAPTCHA detected but solving not implemented yet'\n              };\n            }\n          }\n\n          // If no CAPTCHA detected\n          if (!result.detected) {\n            result = {\n              success: true,\n              type: captchaType,\n              detected: false,\n              message: 'No CAPTCHA detected on page'\n            };\n          }\n        } catch (error) {\n          result = {\n            success: false,\n            type: captchaType,\n            error: error.message\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            captchaResult: result,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Execute custom JavaScript with advanced capabilities\n   */ async executeAdvancedScript(url, script, options) {\n        const waitForSelector = options?.waitForSelector || '';\n        const timeout = options?.timeout || 30000;\n        const returnType = options?.returnType || 'json';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        ${waitForSelector ? `await page.waitForSelector(\"${waitForSelector}\", { timeout: ${timeout} });` : ''}\n\n        // Execute custom script\n        const scriptResult = await page.evaluate(() => {\n          ${script}\n        });\n\n        let finalResult = scriptResult;\n\n        if (\"${returnType}\" === 'screenshot') {\n          const screenshot = await page.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n          finalResult = {\n            scriptResult: scriptResult,\n            screenshot: screenshot\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            result: finalResult,\n            returnType: \"${returnType}\",\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Smart content extraction with multiple strategies\n   */ async smartExtract(url, extractionGoals) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const goals = ${JSON.stringify(extractionGoals)};\n        const results = {};\n\n        // Common extraction patterns\n        const extractors = {\n          prices: () => {\n            const priceSelectors = [\n              '[class*=\"price\"]', '[id*=\"price\"]', '.cost', '.amount',\n              '[data-testid*=\"price\"]', '.currency', '[class*=\"dollar\"]'\n            ];\n            const prices = [];\n            priceSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const text = el.textContent?.trim();\n                if (text && /[$£€¥₹]|\\\\d+\\\\.\\\\d{2}/.test(text)) {\n                  prices.push({\n                    text: text,\n                    selector: selector,\n                    element: el.tagName\n                  });\n                }\n              });\n            });\n            return prices;\n          },\n\n          contact: () => {\n            const contactSelectors = [\n              '[href^=\"mailto:\"]', '[href^=\"tel:\"]', '.contact', '.email', '.phone'\n            ];\n            const contacts = [];\n            contactSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                contacts.push({\n                  text: el.textContent?.trim(),\n                  href: el.getAttribute('href'),\n                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'\n                });\n              });\n            });\n            return contacts;\n          },\n\n          products: () => {\n            const productSelectors = [\n              '.product', '[class*=\"product\"]', '.item', '[data-testid*=\"product\"]'\n            ];\n            const products = [];\n            productSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const title = el.querySelector('h1, h2, h3, .title, [class*=\"title\"]')?.textContent?.trim();\n                const price = el.querySelector('[class*=\"price\"], .cost')?.textContent?.trim();\n                const image = el.querySelector('img')?.src;\n                if (title) {\n                  products.push({ title, price, image });\n                }\n              });\n            });\n            return products;\n          },\n\n          text: () => {\n            // Extract main content\n            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];\n            let content = '';\n            for (const selector of contentSelectors) {\n              const el = document.querySelector(selector);\n              if (el) {\n                content = el.textContent?.trim() || '';\n                break;\n              }\n            }\n            if (!content) {\n              content = document.body.textContent?.trim() || '';\n            }\n            return content.substring(0, 5000); // Limit to 5000 chars\n          },\n\n          links: () => {\n            const links = [];\n            document.querySelectorAll('a[href]').forEach(el => {\n              const href = el.getAttribute('href');\n              const text = el.textContent?.trim();\n              if (href && text && !href.startsWith('#')) {\n                links.push({\n                  url: new URL(href, window.location.href).href,\n                  text: text\n                });\n              }\n            });\n            return links.slice(0, 50); // Limit to 50 links\n          }\n        };\n\n        // Execute extractors based on goals\n        goals.forEach(goal => {\n          const goalLower = goal.toLowerCase();\n          if (goalLower.includes('price') || goalLower.includes('cost')) {\n            results.prices = extractors.prices();\n          }\n          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {\n            results.contact = extractors.contact();\n          }\n          if (goalLower.includes('product') || goalLower.includes('item')) {\n            results.products = extractors.products();\n          }\n          if (goalLower.includes('text') || goalLower.includes('content')) {\n            results.text = extractors.text();\n          }\n          if (goalLower.includes('link') || goalLower.includes('url')) {\n            results.links = extractors.links();\n          }\n        });\n\n        // If no specific goals, extract everything\n        if (goals.length === 0) {\n          Object.keys(extractors).forEach(key => {\n            results[key] = extractors[key]();\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            extractionGoals: goals,\n            results: results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    // Get service statistics\n    getStats() {\n        return {\n            totalKeys: this.apiKeys.length,\n            healthyKeys: this.getHealthyKeys().length,\n            keyUsage: Object.fromEntries(this.keyUsageCount),\n            keyErrors: Object.fromEntries(this.keyErrors)\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowserlessService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browserless.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/BrowsingExecutionService.ts":
/*!******************************************************!*\
  !*** ./src/lib/browsing/BrowsingExecutionService.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrowsingExecutionService: () => (/* binding */ BrowsingExecutionService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n/* harmony import */ var _SmartBrowsingExecutor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SmartBrowsingExecutor */ \"(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts\");\n// Browsing Execution Service - Handles browsing model selection with fallback support\n// Integrates with BrowserlessService for actual web browsing\n\n\nclass BrowsingExecutionService {\n    constructor(){\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n        this.smartBrowsingExecutor = _SmartBrowsingExecutor__WEBPACK_IMPORTED_MODULE_1__.SmartBrowsingExecutor.getInstance();\n    }\n    static getInstance() {\n        if (!BrowsingExecutionService.instance) {\n            BrowsingExecutionService.instance = new BrowsingExecutionService();\n        }\n        return BrowsingExecutionService.instance;\n    }\n    /**\n   * Execute browsing with model fallback support\n   * Now uses SmartBrowsingExecutor for intelligent, plan-based browsing\n   */ async executeBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery, useSmartBrowsing = true) {\n        try {\n            if (!browsingConfig.browsing_enabled) {\n                return {\n                    success: false,\n                    error: 'Browsing is not enabled for this configuration'\n                };\n            }\n            if (!browsingConfig.browsing_models || browsingConfig.browsing_models.length === 0) {\n                return {\n                    success: false,\n                    error: 'No browsing models configured'\n                };\n            }\n            console.log(`[Browsing Execution] Starting ${useSmartBrowsing ? 'SMART' : 'SIMPLE'} browsing`);\n            console.log(`[Browsing Execution] Query: \"${query}\", Type: ${browsingType}`);\n            // Use Smart Browsing for complex tasks\n            if (useSmartBrowsing) {\n                console.log(`[Browsing Execution] 🧠 Using Smart Browsing Executor`);\n                const smartResult = await this.smartBrowsingExecutor.executeSmartBrowsing(refinedQuery || query, browsingConfig, browsingType);\n                if (smartResult.success) {\n                    return {\n                        success: true,\n                        content: smartResult.content,\n                        modelUsed: browsingConfig.browsing_models[0]?.model || 'smart-browsing',\n                        providerUsed: browsingConfig.browsing_models[0]?.provider || 'smart-browsing',\n                        browsingData: smartResult.plan\n                    };\n                } else {\n                    // Check if we should fallback due to network issues\n                    if (smartResult.shouldFallback) {\n                        console.log(`[Browsing Execution] 🌐 Network issue detected, falling back to simple browsing: ${smartResult.error}`);\n                    } else {\n                        console.log(`[Browsing Execution] Smart browsing failed, falling back to simple browsing: ${smartResult.error}`);\n                    }\n                // Fall back to simple browsing\n                }\n            }\n            // Fallback to simple browsing (original logic)\n            console.log(`[Browsing Execution] 🔄 Using Simple Browsing (fallback)`);\n            return await this.executeSimpleBrowsing(query, browsingConfig, browsingType, refinedQuery);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Fatal error:', errorMessage);\n            return {\n                success: false,\n                error: `Browsing execution failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Execute simple browsing (original logic) as fallback\n   */ async executeSimpleBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery) {\n        // Sort models by order for fallback\n        const sortedModels = [\n            ...browsingConfig.browsing_models\n        ].sort((a, b)=>a.order - b.order);\n        console.log(`[Simple Browsing] Starting with ${sortedModels.length} models configured`);\n        let lastError = null;\n        // Try each model in order (strict fallback pattern)\n        for (const model of sortedModels){\n            try {\n                console.log(`[Simple Browsing] Attempting with ${model.provider}/${model.model}`);\n                // First, perform the web browsing\n                const browsingResult = await this.performWebBrowsing(refinedQuery || query, browsingType);\n                if (!browsingResult.success) {\n                    throw new Error(browsingResult.error || 'Browsing failed');\n                }\n                console.log(`[Simple Browsing] ✅ Web browsing successful, got ${JSON.stringify(browsingResult.data).length} characters of data`);\n                // Then, use the AI model to process the browsing results\n                const aiResult = await this.processWithAI(query, browsingResult.data, model, browsingType);\n                if (aiResult.success) {\n                    console.log(`[Simple Browsing] ✅ Success with ${model.provider}/${model.model}`);\n                    return {\n                        success: true,\n                        content: aiResult.content,\n                        modelUsed: model.model,\n                        providerUsed: model.provider,\n                        browsingData: browsingResult.data\n                    };\n                } else {\n                    throw new Error(aiResult.error || 'AI processing failed');\n                }\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n                lastError = errorMessage;\n                console.log(`[Simple Browsing] ❌ Failed with ${model.provider}/${model.model}: ${errorMessage}`);\n                continue;\n            }\n        }\n        // If we get here, all models failed\n        return {\n            success: false,\n            error: `All browsing models failed. Last error: ${lastError}`\n        };\n    }\n    /**\n   * Perform web browsing using BrowserlessService\n   */ async performWebBrowsing(query, browsingType) {\n        try {\n            let result;\n            switch(browsingType){\n                case 'search':\n                    // Use search functionality\n                    result = await this.browserlessService.searchAndExtract(query);\n                    break;\n                case 'navigate':\n                    // Try to extract URL from query or use as-is\n                    const urlMatch = query.match(/https?:\\/\\/[^\\s]+/);\n                    const url = urlMatch ? urlMatch[0] : query;\n                    result = await this.browserlessService.navigateAndExtract(url);\n                    break;\n                case 'extract':\n                    // Similar to navigate but with specific extraction\n                    const extractUrl = query.match(/https?:\\/\\/[^\\s]+/)?.[0] || query;\n                    result = await this.browserlessService.navigateAndExtract(extractUrl);\n                    break;\n                default:\n                    // Default to search\n                    result = await this.browserlessService.searchAndExtract(query);\n            }\n            if (result && result.data) {\n                console.log(`[Browsing Execution] ✅ Web browsing successful, got ${JSON.stringify(result.data).length} characters of data`);\n                return {\n                    success: true,\n                    data: result.data\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No data returned from browsing'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Web browsing failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Process browsing results with AI model\n   */ async processWithAI(originalQuery, browsingData, model, browsingType) {\n        try {\n            const prompt = this.buildProcessingPrompt(originalQuery, browsingData, browsingType);\n            // Call the appropriate AI provider\n            const response = await this.callAIProvider(prompt, model);\n            if (response && response.content) {\n                return {\n                    success: true,\n                    content: response.content\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No content returned from AI model'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Build processing prompt for AI model\n   */ buildProcessingPrompt(originalQuery, browsingData, browsingType) {\n        const dataStr = JSON.stringify(browsingData, null, 2);\n        return `You are an AI assistant that processes web browsing results to answer user queries.\n\nUSER QUERY: \"${originalQuery}\"\nBROWSING TYPE: ${browsingType}\n\nWEB BROWSING RESULTS:\n${dataStr}\n\nINSTRUCTIONS:\n1. Analyze the browsing results carefully\n2. Extract relevant information that answers the user's query\n3. Provide a comprehensive, well-structured response\n4. If the browsing results don't contain relevant information, say so clearly\n5. Include specific details, numbers, dates, and facts from the browsing results\n6. Organize the information in a clear, readable format\n7. Cite sources when possible (URLs, website names, etc.)\n\nPlease provide a helpful response based on the browsing results:`;\n    }\n    /**\n   * Get the correct model ID for API calls (following RouKey's pattern)\n   * OpenRouter keeps full model ID, other providers strip the prefix\n   */ getEffectiveModelId(model) {\n        // For OpenRouter, return the full model ID\n        if (model.provider.toLowerCase() === 'openrouter') {\n            return model.model;\n        }\n        // For other providers, extract the model name after the prefix\n        const parts = model.model.split('/');\n        return parts.length > 1 ? parts[parts.length - 1] : model.model;\n    }\n    /**\n   * Call AI provider based on model configuration\n   */ async callAIProvider(prompt, model) {\n        try {\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            // Get the effective model ID following RouKey's pattern\n            const effectiveModelId = this.getEffectiveModelId(model);\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'openrouter':\n                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`,\n                        'HTTP-Referer': 'https://roukey.online',\n                        'X-Title': 'RouKey'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider: ${model.provider}`);\n            }\n            console.log(`[Browsing AI] Calling ${model.provider} API with model ${effectiveModelId} (original: ${model.model})...`);\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000) // 30s timeout\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(`[Browsing AI] API error: ${response.status} - ${errorText}`);\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(`[Browsing AI] Raw response:`, JSON.stringify(result, null, 2));\n            // Extract content based on provider response format\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                // For OpenAI-compatible APIs (including Google's OpenAI-compatible endpoint)\n                content = result.choices?.[0]?.message?.content;\n            }\n            console.log(`[Browsing AI] Extracted content length: ${content?.length || 0}`);\n            if (!content || content.trim().length === 0) {\n                console.error(`[Browsing AI] No content extracted from response. Full response:`, result);\n                return {\n                    error: 'No content returned from AI model - empty response'\n                };\n            }\n            return {\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error(`[Browsing AI] Error calling AI provider:`, errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowsingExecutionService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/BrowsingExecutionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts":
/*!***************************************************!*\
  !*** ./src/lib/browsing/SmartBrowsingExecutor.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartBrowsingExecutor: () => (/* binding */ SmartBrowsingExecutor)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n// Smart Browsing Executor - Intelligent plan-based browsing with todo list management\n// Handles complex browsing tasks by creating plans, executing subtasks, and updating progress\n\nclass SmartBrowsingExecutor {\n    constructor(){\n        this.activePlans = new Map();\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n    }\n    static getInstance() {\n        if (!SmartBrowsingExecutor.instance) {\n            SmartBrowsingExecutor.instance = new SmartBrowsingExecutor();\n        }\n        return SmartBrowsingExecutor.instance;\n    }\n    /**\n   * Execute smart browsing with planning and todo list management\n   */ async executeSmartBrowsing(query, browsingConfig, browsingType = 'search') {\n        try {\n            console.log(`[Smart Browsing] 🎯 Starting intelligent browsing for: \"${query}\"`);\n            // Step 1: Create a browsing plan\n            const plan = await this.createBrowsingPlan(query, browsingType, browsingConfig);\n            this.activePlans.set(plan.id, plan);\n            console.log(`[Smart Browsing] 📋 Created plan with ${plan.subtasks.length} subtasks`);\n            this.logPlan(plan);\n            // Step 2: Execute the plan\n            const result = await this.executePlan(plan, browsingConfig);\n            if (result.success) {\n                console.log(`[Smart Browsing] ✅ Plan completed successfully`);\n                return {\n                    success: true,\n                    content: result.content,\n                    plan: plan\n                };\n            } else {\n                console.log(`[Smart Browsing] ❌ Plan failed: ${result.error}`);\n                // Check if this is a network connectivity issue\n                if (this.isNetworkError(result.error)) {\n                    console.log(`[Smart Browsing] 🌐 Network connectivity issue detected, recommending fallback to simple browsing`);\n                    return {\n                        success: false,\n                        error: `Network connectivity issue: ${result.error}. Falling back to simple browsing.`,\n                        plan: plan,\n                        shouldFallback: true\n                    };\n                }\n                return {\n                    success: false,\n                    error: result.error,\n                    plan: plan\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Smart Browsing] Fatal error:', errorMessage);\n            // Check if this is a network connectivity issue\n            if (this.isNetworkError(errorMessage)) {\n                console.log(`[Smart Browsing] 🌐 Network connectivity issue detected, recommending fallback to simple browsing`);\n                return {\n                    success: false,\n                    error: `Network connectivity issue: ${errorMessage}. Falling back to simple browsing.`,\n                    shouldFallback: true\n                };\n            }\n            return {\n                success: false,\n                error: `Smart browsing failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Check if an error is related to network connectivity\n   */ isNetworkError(errorMessage) {\n        const networkErrorPatterns = [\n            'fetch failed',\n            'ECONNRESET',\n            'ENOTFOUND',\n            'ETIMEDOUT',\n            'ECONNREFUSED',\n            'Network request failed',\n            'Connection timeout',\n            'DNS resolution failed'\n        ];\n        return networkErrorPatterns.some((pattern)=>errorMessage.toLowerCase().includes(pattern.toLowerCase()));\n    }\n    /**\n   * Create an intelligent browsing plan based on the query\n   */ async createBrowsingPlan(query, browsingType, browsingConfig) {\n        const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        // Use AI to create a smart plan\n        const planningResult = await this.generatePlanWithAI(query, browsingType, browsingConfig);\n        const plan = {\n            id: planId,\n            originalQuery: query,\n            goal: planningResult.goal || `Find comprehensive information about: ${query}`,\n            subtasks: planningResult.subtasks || this.createFallbackPlan(query, browsingType),\n            status: 'planning',\n            progress: 0,\n            gatheredData: {},\n            visitedUrls: [],\n            searchQueries: [],\n            completedSubtasks: [],\n            failedSubtasks: [],\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        return plan;\n    }\n    /**\n   * Generate a browsing plan using AI\n   */ async generatePlanWithAI(query, browsingType, browsingConfig) {\n        try {\n            const model = browsingConfig.browsing_models[0]; // Use first available model for planning\n            if (!model) {\n                throw new Error('No browsing models available for planning');\n            }\n            const planningPrompt = this.buildPlanningPrompt(query, browsingType);\n            const aiResult = await this.callAIForPlanning(planningPrompt, model);\n            if (aiResult.success && aiResult.content) {\n                return this.parsePlanFromAI(aiResult.content, query);\n            } else {\n                console.warn('[Smart Browsing] AI planning failed, using fallback plan');\n                return {\n                    goal: `Find information about: ${query}`,\n                    subtasks: this.createFallbackPlan(query, browsingType)\n                };\n            }\n        } catch (error) {\n            console.warn('[Smart Browsing] AI planning error, using fallback:', error);\n            return {\n                goal: `Find information about: ${query}`,\n                subtasks: this.createFallbackPlan(query, browsingType)\n            };\n        }\n    }\n    /**\n   * Build a comprehensive planning prompt for AI\n   */ buildPlanningPrompt(query, browsingType) {\n        return `You are an expert web browsing strategist. Create a detailed browsing plan to thoroughly research this query: \"${query}\"\n\nBROWSING TYPE: ${browsingType}\n\nCreate a JSON response with this structure:\n{\n  \"goal\": \"Clear statement of what we want to achieve\",\n  \"subtasks\": [\n    {\n      \"id\": \"unique_id\",\n      \"type\": \"search|navigate|extract|analyze\",\n      \"description\": \"What this subtask does\",\n      \"query\": \"Specific search query or URL\",\n      \"priority\": 1-10,\n      \"searchTerms\": [\"alternative\", \"search\", \"terms\"],\n      \"expectedInfo\": \"What information we expect to find\"\n    }\n  ]\n}\n\nGUIDELINES:\n1. Start with broad searches, then get more specific\n2. Use multiple search strategies and terms\n3. Include fact-checking and verification steps\n4. Plan for 3-7 subtasks maximum\n5. Make search terms diverse and comprehensive\n6. Consider different angles and perspectives\n7. Include analysis steps to synthesize information\n\nCreate a smart, thorough plan:`;\n    }\n    /**\n   * Create a fallback plan when AI planning fails\n   */ createFallbackPlan(query, browsingType) {\n        const baseId = Date.now();\n        return [\n            {\n                id: `search_${baseId}_1`,\n                type: 'search',\n                description: 'Primary search for main topic',\n                query: query,\n                status: 'pending',\n                priority: 10,\n                attempts: 0,\n                maxAttempts: 3,\n                searchTerms: [\n                    query,\n                    `${query} information`,\n                    `${query} details`\n                ],\n                expectedInfo: 'General information about the topic'\n            },\n            {\n                id: `search_${baseId}_2`,\n                type: 'search',\n                description: 'Secondary search for additional details',\n                query: `${query} latest news recent updates`,\n                status: 'pending',\n                priority: 8,\n                attempts: 0,\n                maxAttempts: 3,\n                searchTerms: [\n                    `${query} news`,\n                    `${query} updates`,\n                    `${query} recent`\n                ],\n                expectedInfo: 'Recent developments and news'\n            },\n            {\n                id: `analyze_${baseId}`,\n                type: 'analyze',\n                description: 'Analyze and synthesize gathered information',\n                query: 'synthesize findings',\n                status: 'pending',\n                priority: 5,\n                attempts: 0,\n                maxAttempts: 1,\n                dependencies: [\n                    `search_${baseId}_1`,\n                    `search_${baseId}_2`\n                ],\n                expectedInfo: 'Comprehensive summary of findings'\n            }\n        ];\n    }\n    /**\n   * Execute the browsing plan step by step\n   */ async executePlan(plan, browsingConfig) {\n        plan.status = 'executing';\n        plan.updatedAt = new Date().toISOString();\n        console.log(`[Smart Browsing] 🚀 Starting plan execution`);\n        try {\n            // Execute subtasks in priority order, respecting dependencies\n            while(this.hasRemainingTasks(plan)){\n                const nextTask = this.getNextExecutableTask(plan);\n                if (!nextTask) {\n                    console.log(`[Smart Browsing] ⏸️ No executable tasks remaining, checking if plan is complete`);\n                    break;\n                }\n                console.log(`[Smart Browsing] 🔄 Executing subtask: ${nextTask.description}`);\n                plan.currentSubtask = nextTask.id;\n                nextTask.status = 'in_progress';\n                const taskResult = await this.executeSubtask(nextTask, plan, browsingConfig);\n                if (taskResult.success) {\n                    nextTask.status = 'completed';\n                    nextTask.result = taskResult.data;\n                    plan.completedSubtasks.push(nextTask.id);\n                    console.log(`[Smart Browsing] ✅ Subtask completed: ${nextTask.description}`);\n                } else {\n                    nextTask.attempts++;\n                    nextTask.error = taskResult.error;\n                    if (nextTask.attempts >= nextTask.maxAttempts) {\n                        nextTask.status = 'failed';\n                        plan.failedSubtasks.push(nextTask.id);\n                        console.log(`[Smart Browsing] ❌ Subtask failed permanently: ${nextTask.description}`);\n                    } else {\n                        nextTask.status = 'pending';\n                        console.log(`[Smart Browsing] 🔄 Subtask failed, will retry (${nextTask.attempts}/${nextTask.maxAttempts}): ${nextTask.description}`);\n                    }\n                }\n                // Update progress\n                plan.progress = this.calculateProgress(plan);\n                plan.updatedAt = new Date().toISOString();\n                this.logProgress(plan);\n            }\n            // Generate final result\n            const finalResult = await this.synthesizeFinalResult(plan, browsingConfig);\n            plan.finalResult = finalResult;\n            plan.status = 'completed';\n            plan.progress = 100;\n            return {\n                success: true,\n                content: finalResult\n            };\n        } catch (error) {\n            plan.status = 'failed';\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Smart Browsing] Plan execution failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Check if there are remaining tasks to execute\n   */ hasRemainingTasks(plan) {\n        return plan.subtasks.some((task)=>task.status === 'pending' || task.status === 'in_progress');\n    }\n    /**\n   * Get the next executable task (highest priority, dependencies met)\n   */ getNextExecutableTask(plan) {\n        const executableTasks = plan.subtasks.filter((task)=>{\n            if (task.status !== 'pending') return false;\n            // Check if dependencies are met\n            if (task.dependencies && task.dependencies.length > 0) {\n                return task.dependencies.every((depId)=>plan.completedSubtasks.includes(depId));\n            }\n            return true;\n        });\n        // Sort by priority (highest first)\n        executableTasks.sort((a, b)=>b.priority - a.priority);\n        return executableTasks[0] || null;\n    }\n    /**\n   * Execute a single subtask\n   */ async executeSubtask(subtask, plan, browsingConfig) {\n        try {\n            switch(subtask.type){\n                case 'search':\n                    return await this.executeSearchSubtask(subtask, plan);\n                case 'navigate':\n                    return await this.executeNavigateSubtask(subtask, plan);\n                case 'extract':\n                    return await this.executeExtractSubtask(subtask, plan);\n                case 'analyze':\n                    return await this.executeAnalyzeSubtask(subtask, plan, browsingConfig);\n                default:\n                    throw new Error(`Unknown subtask type: ${subtask.type}`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Execute a search subtask with smart query refinement\n   */ async executeSearchSubtask(subtask, plan) {\n        const searchTerms = [\n            subtask.query,\n            ...subtask.searchTerms || []\n        ];\n        let lastError = '';\n        // Try different search terms until we get good results\n        for (const searchTerm of searchTerms){\n            try {\n                console.log(`[Smart Browsing] 🔍 Searching for: \"${searchTerm}\"`);\n                plan.searchQueries.push(searchTerm);\n                const result = await this.browserlessService.searchAndExtract(searchTerm);\n                if (result.data && result.data.results && result.data.results.length > 0) {\n                    console.log(`[Smart Browsing] ✅ Found ${result.data.results.length} results for: \"${searchTerm}\"`);\n                    // Store URLs we've found\n                    result.data.results.forEach((item)=>{\n                        if (item.link && !plan.visitedUrls.includes(item.link)) {\n                            plan.visitedUrls.push(item.link);\n                        }\n                    });\n                    return {\n                        success: true,\n                        data: result.data\n                    };\n                } else {\n                    lastError = `No results found for: \"${searchTerm}\"`;\n                    console.log(`[Smart Browsing] ⚠️ ${lastError}`);\n                }\n            } catch (error) {\n                lastError = error instanceof Error ? error.message : 'Search failed';\n                console.log(`[Smart Browsing] ❌ Search error for \"${searchTerm}\": ${lastError}`);\n            }\n        }\n        return {\n            success: false,\n            error: `All search terms failed. Last error: ${lastError}`\n        };\n    }\n    /**\n   * Execute navigate subtask\n   */ async executeNavigateSubtask(subtask, plan) {\n        try {\n            const url = subtask.query;\n            console.log(`[Smart Browsing] 🌐 Navigating to: ${url}`);\n            if (!plan.visitedUrls.includes(url)) {\n                plan.visitedUrls.push(url);\n            }\n            const result = await this.browserlessService.navigateAndExtract(url);\n            if (result.data) {\n                return {\n                    success: true,\n                    data: result.data\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No data extracted from navigation'\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Navigation failed'\n            };\n        }\n    }\n    /**\n   * Execute extract subtask\n   */ async executeExtractSubtask(subtask, plan) {\n        // Similar to navigate but with specific extraction focus\n        return this.executeNavigateSubtask(subtask, plan);\n    }\n    /**\n   * Execute analyze subtask - synthesize gathered information\n   */ async executeAnalyzeSubtask(subtask, plan, browsingConfig) {\n        try {\n            console.log(`[Smart Browsing] 🧠 Analyzing gathered data...`);\n            // Collect all data from completed subtasks\n            const gatheredData = plan.subtasks.filter((task)=>task.status === 'completed' && task.result).map((task)=>({\n                    type: task.type,\n                    description: task.description,\n                    query: task.query,\n                    data: task.result\n                }));\n            if (gatheredData.length === 0) {\n                return {\n                    success: false,\n                    error: 'No data available for analysis'\n                };\n            }\n            // Use AI to analyze and synthesize the data\n            const model = browsingConfig.browsing_models[0];\n            if (!model) {\n                return {\n                    success: false,\n                    error: 'No AI model available for analysis'\n                };\n            }\n            const analysisPrompt = this.buildAnalysisPrompt(plan.originalQuery, gatheredData);\n            const aiResult = await this.callAIForAnalysis(analysisPrompt, model);\n            if (aiResult.success && aiResult.content) {\n                return {\n                    success: true,\n                    data: {\n                        analysis: aiResult.content,\n                        sourceData: gatheredData\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    error: aiResult.error || 'Analysis failed'\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Analysis failed'\n            };\n        }\n    }\n    /**\n   * Calculate progress percentage\n   */ calculateProgress(plan) {\n        const totalTasks = plan.subtasks.length;\n        const completedTasks = plan.completedSubtasks.length;\n        return Math.round(completedTasks / totalTasks * 100);\n    }\n    /**\n   * Log progress update\n   */ logProgress(plan) {\n        const completed = plan.completedSubtasks.length;\n        const failed = plan.failedSubtasks.length;\n        const total = plan.subtasks.length;\n        const remaining = total - completed - failed;\n        console.log(`[Smart Browsing] 📊 Progress: ${plan.progress}% (${completed}/${total} completed, ${failed} failed, ${remaining} remaining)`);\n    }\n    /**\n   * Build analysis prompt for AI\n   */ buildAnalysisPrompt(originalQuery, gatheredData) {\n        const dataContext = gatheredData.map((item, index)=>`Source ${index + 1} (${item.type}): ${item.description}\\nQuery: ${item.query}\\nData: ${JSON.stringify(item.data, null, 2)}`).join('\\n\\n---\\n\\n');\n        return `You are an expert information analyst. Analyze the following browsing data and provide a comprehensive answer to the original query.\n\nORIGINAL QUERY: \"${originalQuery}\"\n\nGATHERED DATA:\n${dataContext}\n\nPlease provide:\n1. A comprehensive answer to the original query\n2. Key findings and insights\n3. Any conflicting information found\n4. Confidence level in the findings\n5. Recommendations for further research if needed\n\nFormat your response as a clear, well-structured analysis that directly addresses the user's query.`;\n    }\n    /**\n   * Call AI for planning\n   */ async callAIForPlanning(prompt, model) {\n        try {\n            const effectiveModelId = this.getEffectiveModelId(model);\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider (same as BrowsingExecutionService)\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'openrouter':\n                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`,\n                        'HTTP-Referer': 'https://roukey.online',\n                        'X-Title': 'RouKey'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider for planning: ${model.provider}`);\n            }\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                content = result.choices?.[0]?.message?.content;\n            }\n            if (!content) {\n                throw new Error('No content returned from AI model');\n            }\n            return {\n                success: true,\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Call AI for analysis (same as planning but different purpose)\n   */ async callAIForAnalysis(prompt, model) {\n        return this.callAIForPlanning(prompt, model); // Same implementation\n    }\n    /**\n   * Get effective model ID (same logic as BrowsingExecutionService)\n   */ getEffectiveModelId(model) {\n        if (model.provider.toLowerCase() === 'openrouter') {\n            return model.model;\n        }\n        const parts = model.model.split('/');\n        return parts.length > 1 ? parts[parts.length - 1] : model.model;\n    }\n    /**\n   * Parse plan from AI response\n   */ parsePlanFromAI(aiResponse, originalQuery) {\n        try {\n            // Try to extract JSON from the response\n            const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n            if (!jsonMatch) {\n                throw new Error('No JSON found in AI response');\n            }\n            const parsed = JSON.parse(jsonMatch[0]);\n            if (!parsed.goal || !parsed.subtasks || !Array.isArray(parsed.subtasks)) {\n                throw new Error('Invalid plan structure from AI');\n            }\n            // Convert AI subtasks to our format\n            const subtasks = parsed.subtasks.map((task, index)=>({\n                    id: task.id || `ai_task_${Date.now()}_${index}`,\n                    type: task.type || 'search',\n                    description: task.description || `Task ${index + 1}`,\n                    query: task.query || originalQuery,\n                    status: 'pending',\n                    priority: task.priority || 5,\n                    attempts: 0,\n                    maxAttempts: 3,\n                    searchTerms: task.searchTerms || [],\n                    expectedInfo: task.expectedInfo || ''\n                }));\n            return {\n                goal: parsed.goal,\n                subtasks\n            };\n        } catch (error) {\n            console.warn('[Smart Browsing] Failed to parse AI plan, using fallback:', error);\n            return {\n                goal: `Find information about: ${originalQuery}`,\n                subtasks: this.createFallbackPlan(originalQuery, 'search')\n            };\n        }\n    }\n    /**\n   * Synthesize final result from all gathered data\n   */ async synthesizeFinalResult(plan, browsingConfig) {\n        try {\n            // Find the analysis result if available\n            const analysisTask = plan.subtasks.find((task)=>task.type === 'analyze' && task.status === 'completed' && task.result);\n            if (analysisTask && analysisTask.result?.analysis) {\n                return analysisTask.result.analysis;\n            }\n            // If no analysis task, create a summary from all completed tasks\n            const completedTasks = plan.subtasks.filter((task)=>task.status === 'completed' && task.result);\n            if (completedTasks.length === 0) {\n                return `No information was successfully gathered for the query: \"${plan.originalQuery}\"`;\n            }\n            // Create a basic summary\n            let summary = `Based on browsing research for \"${plan.originalQuery}\":\\n\\n`;\n            completedTasks.forEach((task, index)=>{\n                summary += `${index + 1}. ${task.description}:\\n`;\n                if (task.result?.results && Array.isArray(task.result.results)) {\n                    task.result.results.slice(0, 3).forEach((result)=>{\n                        summary += `   • ${result.title || 'Result'}\\n`;\n                    });\n                } else if (typeof task.result === 'string') {\n                    summary += `   ${task.result.substring(0, 200)}...\\n`;\n                }\n                summary += '\\n';\n            });\n            return summary;\n        } catch (error) {\n            console.error('[Smart Browsing] Error synthesizing final result:', error);\n            return `Research completed for \"${plan.originalQuery}\" but encountered errors in synthesis. Please check the individual results.`;\n        }\n    }\n    /**\n   * Log the browsing plan for debugging\n   */ logPlan(plan) {\n        console.log(`[Smart Browsing] 📋 BROWSING PLAN:`);\n        console.log(`[Smart Browsing] Goal: ${plan.goal}`);\n        console.log(`[Smart Browsing] Subtasks:`);\n        plan.subtasks.forEach((subtask, index)=>{\n            console.log(`[Smart Browsing]   ${index + 1}. [${subtask.type.toUpperCase()}] ${subtask.description}`);\n            console.log(`[Smart Browsing]      Query: \"${subtask.query}\"`);\n            console.log(`[Smart Browsing]      Priority: ${subtask.priority}, Status: ${subtask.status}`);\n            if (subtask.searchTerms && subtask.searchTerms.length > 0) {\n                console.log(`[Smart Browsing]      Alt terms: ${subtask.searchTerms.join(', ')}`);\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts\n");

/***/ })

};
;