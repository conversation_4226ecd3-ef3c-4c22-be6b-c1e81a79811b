"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_browsing_BrowsingExecutionService_ts";
exports.ids = ["_rsc_src_lib_browsing_BrowsingExecutionService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/browserless.ts":
/*!********************************!*\
  !*** ./src/lib/browserless.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browserless.io API service with key rotation\n// Similar to Jina API key rotation system\nclass BrowserlessService {\n    constructor(){\n        this.apiKeys = [];\n        this.currentKeyIndex = 0;\n        this.keyUsageCount = new Map();\n        this.keyErrors = new Map();\n        this.MAX_RETRIES = 3;\n        this.ERROR_THRESHOLD = 5;\n        this.ENDPOINT = 'https://production-sfo.browserless.io';\n        this.initializeKeys();\n        this.testConnectivity();\n    }\n    static getInstance() {\n        if (!BrowserlessService.instance) {\n            BrowserlessService.instance = new BrowserlessService();\n        }\n        return BrowserlessService.instance;\n    }\n    initializeKeys() {\n        // Load all Browserless API keys from environment (similar to Jina pattern)\n        this.apiKeys = [\n            process.env.BROWSERLESS_API_KEY,\n            process.env.BROWSERLESS_API_KEY_2,\n            process.env.BROWSERLESS_API_KEY_3,\n            process.env.BROWSERLESS_API_KEY_4,\n            process.env.BROWSERLESS_API_KEY_5,\n            process.env.BROWSERLESS_API_KEY_6,\n            process.env.BROWSERLESS_API_KEY_7,\n            process.env.BROWSERLESS_API_KEY_8,\n            process.env.BROWSERLESS_API_KEY_9,\n            process.env.BROWSERLESS_API_KEY_10\n        ].filter(Boolean);\n        if (this.apiKeys.length === 0) {\n            console.error('No Browserless API keys found in environment variables');\n            return;\n        }\n        console.log(`[Browserless] Initialized with ${this.apiKeys.length} API keys`);\n        // Initialize usage tracking\n        this.apiKeys.forEach((key)=>{\n            this.keyUsageCount.set(key, 0);\n            this.keyErrors.set(key, 0);\n        });\n    }\n    /**\n   * Test connectivity to Browserless service\n   */ async testConnectivity() {\n        if (this.apiKeys.length === 0) {\n            console.warn('[Browserless] No API keys available for connectivity test');\n            return;\n        }\n        try {\n            const testKey = this.apiKeys[0];\n            const testUrl = `${this.ENDPOINT}/function?token=${testKey}`;\n            // Simple connectivity test with proper function format\n            const testCode = `\n        export default async function ({ page }) {\n          return {\n            status: \"connectivity-test-success\",\n            timestamp: new Date().toISOString()\n          };\n        }\n      `;\n            const response = await fetch(testUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/javascript'\n                },\n                body: testCode,\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const result = await response.text();\n                console.log('[Browserless] ✅ Connectivity test successful:', result);\n            } else {\n                const errorText = await response.text();\n                console.warn(`[Browserless] ⚠️ Connectivity test failed with status: ${response.status}`);\n                console.warn(`[Browserless] Error details: ${errorText}`);\n                console.warn(`[Browserless] Using key: ${testKey.substring(0, 8)}...`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.warn(`[Browserless] ⚠️ Connectivity test failed: ${errorMessage}`);\n            // Check for specific network errors\n            if (errorMessage.includes('ENOTFOUND')) {\n                console.error('[Browserless] 🌐 DNS resolution failed - check if chrome.browserless.io is accessible');\n            } else if (errorMessage.includes('ECONNRESET')) {\n                console.error('[Browserless] 🔌 Connection reset - possible network or service issue');\n            }\n        }\n    }\n    getNextApiKey() {\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Browserless API keys available');\n        }\n        // Find the key with lowest usage and errors\n        let bestKey = this.apiKeys[0];\n        let bestScore = this.calculateKeyScore(bestKey);\n        for (const key of this.apiKeys){\n            const score = this.calculateKeyScore(key);\n            if (score < bestScore) {\n                bestKey = key;\n                bestScore = score;\n            }\n        }\n        return bestKey;\n    }\n    calculateKeyScore(key) {\n        const usage = this.keyUsageCount.get(key) || 0;\n        const errors = this.keyErrors.get(key) || 0;\n        // Higher score = worse key (more usage + more errors)\n        return usage + errors * 10;\n    }\n    incrementKeyUsage(key) {\n        const currentUsage = this.keyUsageCount.get(key) || 0;\n        this.keyUsageCount.set(key, currentUsage + 1);\n    }\n    incrementKeyError(key) {\n        const currentErrors = this.keyErrors.get(key) || 0;\n        this.keyErrors.set(key, currentErrors + 1);\n    }\n    isKeyHealthy(key) {\n        const errors = this.keyErrors.get(key) || 0;\n        return errors < this.ERROR_THRESHOLD;\n    }\n    getHealthyKeys() {\n        return this.apiKeys.filter((key)=>this.isKeyHealthy(key));\n    }\n    async executeFunction(code, context, config) {\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            // Reset error counts if all keys are unhealthy\n            this.keyErrors.clear();\n            this.apiKeys.forEach((key)=>this.keyErrors.set(key, 0));\n            console.log('All Browserless keys were unhealthy, resetting error counts');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const response = await this.makeRequest(apiKey, code, context, config);\n                // Success - return the response\n                return response;\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless attempt ${attempt + 1} failed:`, error);\n                // If it's a rate limit or quota error, mark the key as having an error\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless API attempts failed');\n    }\n    async makeRequest(apiKey, code, context, config) {\n        const url = `${this.ENDPOINT}/function?token=${apiKey}`;\n        const requestBody = context ? {\n            code,\n            context\n        } : code;\n        const headers = {\n            'Content-Type': context ? 'application/json' : 'application/javascript',\n            'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'\n        };\n        const response = await fetch(url, {\n            method: 'POST',\n            headers,\n            body: context ? JSON.stringify(requestBody) : code,\n            signal: AbortSignal.timeout(config?.timeout || 30000)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Browserless API error: ${response.status} - ${errorText}`);\n        }\n        const result = await response.json();\n        return result;\n    }\n    isRateLimitError(error) {\n        const message = error.message.toLowerCase();\n        return message.includes('rate limit') || message.includes('quota') || message.includes('429') || message.includes('too many requests');\n    }\n    // Convenience methods for common browser tasks\n    async navigateAndExtract(url, selector) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n        \n        const title = await page.title();\n        const content = ${selector ? `await page.$eval(\"${selector}\", el => el.textContent || el.innerText)` : 'await page.evaluate(() => document.body.innerText)'};\n        \n        return {\n          data: {\n            url: \"${url}\",\n            title,\n            content: content?.trim() || \"\"\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async searchAndExtractUnblocked(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            throw new Error('No healthy Browserless API keys available');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const url = `${this.ENDPOINT}/unblock?token=${apiKey}`;\n                const requestBody = {\n                    url: searchUrl,\n                    content: true,\n                    browserWSEndpoint: false,\n                    cookies: false,\n                    screenshot: false,\n                    waitForSelector: {\n                        selector: 'h3, .g h3, .LC20lb, .b_algo h2',\n                        timeout: 10000\n                    }\n                };\n                const response = await fetch(url, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'User-Agent': 'RouKey-Browser-Agent/1.0'\n                    },\n                    body: JSON.stringify(requestBody),\n                    signal: AbortSignal.timeout(120000) // Increased to 2 minutes for complex searches\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`Browserless unblock API error: ${response.status} - ${errorText}`);\n                }\n                const result = await response.json();\n                if (result.content) {\n                    // Parse the HTML content to extract search results\n                    const searchResults = this.parseSearchResults(result.content, searchEngine, query);\n                    return {\n                        data: {\n                            query,\n                            searchEngine,\n                            results: searchResults,\n                            timestamp: new Date().toISOString(),\n                            debug: {\n                                pageTitle: 'Unblocked search',\n                                pageUrl: searchUrl,\n                                totalElements: searchResults.length,\n                                usedSelector: 'unblock-api',\n                                extractedCount: searchResults.length\n                            }\n                        },\n                        type: \"application/json\"\n                    };\n                } else {\n                    throw new Error('No content returned from unblock API');\n                }\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless unblock attempt ${attempt + 1} failed:`, error);\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless unblock API attempts failed');\n    }\n    parseSearchResults(htmlContent, searchEngine, query) {\n        // Simple regex-based parsing for search results\n        // This is a basic implementation - could be enhanced with a proper HTML parser\n        const results = [];\n        if (searchEngine === 'google') {\n            // Google search result patterns\n            const titleRegex = /<h3[^>]*>([^<]+)<\\/h3>/gi;\n            const linkRegex = /<a[^>]*href=\"([^\"]*)\"[^>]*>/gi;\n            let titleMatch;\n            let linkMatch;\n            const titles = [];\n            const links = [];\n            // Extract titles\n            while((titleMatch = titleRegex.exec(htmlContent)) !== null){\n                titles.push(titleMatch[1].trim());\n            }\n            // Extract links (filter Google URLs)\n            while((linkMatch = linkRegex.exec(htmlContent)) !== null){\n                const link = linkMatch[1];\n                if (link.startsWith('http') && !link.includes('google.com') && !link.includes('/search?')) {\n                    links.push(link);\n                }\n            }\n            // Combine titles and links\n            const maxResults = Math.min(titles.length, links.length, 5);\n            for(let i = 0; i < maxResults; i++){\n                if (titles[i] && links[i]) {\n                    results.push({\n                        title: titles[i],\n                        link: links[i]\n                    });\n                }\n            }\n        } else {\n            // Bing search result patterns\n            const resultRegex = /<h2[^>]*><a[^>]*href=\"([^\"]*)\"[^>]*>([^<]+)<\\/a><\\/h2>/gi;\n            let match;\n            while((match = resultRegex.exec(htmlContent)) !== null && results.length < 5){\n                results.push({\n                    title: match[2].trim(),\n                    link: match[1]\n                });\n            }\n        }\n        return results;\n    }\n    async searchAndExtract(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const code = `\n      export default async function ({ page }) {\n        // Set a realistic user agent and headers\n        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');\n        await page.setExtraHTTPHeaders({\n          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n          'Accept-Language': 'en-US,en;q=0.5',\n          'Accept-Encoding': 'gzip, deflate',\n          'DNT': '1',\n          'Connection': 'keep-alive',\n          'Upgrade-Insecure-Requests': '1',\n        });\n\n        await page.goto(\"${searchUrl}\", { waitUntil: 'networkidle0' });\n\n        // Set up CAPTCHA solving\n        const cdp = await page.createCDPSession();\n\n        // Check for CAPTCHA and solve if found\n        let captchaFound = false;\n        cdp.on('Browserless.captchaFound', () => {\n          console.log('CAPTCHA detected on search page');\n          captchaFound = true;\n        });\n\n        // Wait a moment to see if CAPTCHA is detected\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        if (captchaFound) {\n          console.log('Attempting to solve CAPTCHA...');\n          try {\n            const { solved, error } = await cdp.send('Browserless.solveCaptcha');\n            console.log('CAPTCHA solving result:', { solved, error });\n\n            if (solved) {\n              console.log('CAPTCHA solved successfully');\n              // Wait for page to reload after CAPTCHA solving\n              await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 10000 }).catch(() => {\n                console.log('No navigation after CAPTCHA solve, continuing...');\n              });\n            } else {\n              console.log('CAPTCHA solving failed:', error);\n            }\n          } catch (captchaError) {\n            console.log('CAPTCHA solving error:', captchaError);\n          }\n        }\n\n        // Wait for search results to load with multiple fallback selectors\n        let resultsLoaded = false;\n        const googleSelectors = ['[data-ved]', 'h3', '.g h3', '.LC20lb', '.DKV0Md', '#search h3'];\n        const bingSelectors = ['.b_algo', '.b_algo h2', 'h2 a'];\n\n        const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n\n        for (const selector of selectorsToTry) {\n          try {\n            await page.waitForSelector(selector, { timeout: 3000 });\n            resultsLoaded = true;\n            console.log('Found results with selector:', selector);\n            break;\n          } catch (e) {\n            console.log('Selector failed:', selector);\n            continue;\n          }\n        }\n\n        if (!resultsLoaded) {\n          // Give it one more chance with a longer timeout on the most common selector\n          try {\n            await page.waitForSelector('${searchEngine === 'google' ? 'h3' : '.b_algo'}', { timeout: 5000 });\n          } catch (e) {\n            console.log('All selectors failed, proceeding anyway...');\n          }\n        }\n\n        const results = await page.evaluate(() => {\n          console.log('Starting search results extraction...');\n\n          // Try multiple selectors for extracting results\n          const googleSelectors = [\n            '[data-ved] h3',\n            'h3',\n            '.g h3',\n            '.LC20lb',\n            '.DKV0Md',\n            '#search h3',\n            '.yuRUbf h3',\n            'a h3',\n            '[role=\"heading\"]'\n          ];\n          const bingSelectors = ['.b_algo h2', '.b_algo h2 a', 'h2 a'];\n\n          const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n          let elements = [];\n          let usedSelector = '';\n\n          // Debug: Log page content\n          console.log('Page title:', document.title);\n          console.log('Page URL:', window.location.href);\n          console.log('Page body preview:', document.body.innerText.substring(0, 500));\n\n          for (const selector of selectorsToTry) {\n            elements = document.querySelectorAll(selector);\n            console.log('Trying selector:', selector, 'found:', elements.length, 'elements');\n            if (elements.length > 0) {\n              usedSelector = selector;\n              console.log('Found', elements.length, 'results with selector:', selector);\n              break;\n            }\n          }\n\n          // If no results found, try a more generic approach\n          if (elements.length === 0) {\n            console.log('No results with specific selectors, trying generic approach...');\n            // Try to find any links that look like search results\n            const allLinks = document.querySelectorAll('a[href*=\"/url?\"]');\n            console.log('Found', allLinks.length, 'Google result links');\n\n            if (allLinks.length > 0) {\n              elements = Array.from(allLinks).map(link => {\n                const h3 = link.querySelector('h3');\n                return h3 || link;\n              }).filter(el => el && el.textContent?.trim());\n              usedSelector = 'a[href*=\"/url?\"] h3 (fallback)';\n              console.log('Using fallback approach, found', elements.length, 'elements');\n            }\n          }\n\n          const extractedResults = Array.from(elements).slice(0, 5).map(el => {\n            const title = el.textContent?.trim() || '';\n            let link = '';\n\n            // Try to get the link\n            if (el.href) {\n              link = el.href;\n            } else {\n              const closestLink = el.closest('a');\n              if (closestLink) {\n                link = closestLink.href;\n              }\n            }\n\n            return { title, link };\n          }).filter(item => item.title && item.link);\n\n          console.log('Final results:', extractedResults.length, 'items using selector:', usedSelector);\n\n          return {\n            results: extractedResults,\n            debug: {\n              pageTitle: document.title,\n              pageUrl: window.location.href,\n              totalElements: elements.length,\n              usedSelector: usedSelector || 'none',\n              extractedCount: extractedResults.length\n            }\n          };\n        });\n\n        return {\n          data: {\n            query: \"${query}\",\n            searchEngine: \"${searchEngine}\",\n            results: results.results,\n            timestamp: new Date().toISOString(),\n            debug: results.debug\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code, null, {\n            timeout: 60000 // Increase timeout for CAPTCHA solving\n        });\n    }\n    async takeScreenshot(url, options) {\n        const fullPage = options?.fullPage ?? false;\n        const selector = options?.selector || '';\n        const quality = options?.quality || 80;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        let screenshot;\n        if (\"${selector}\") {\n          // Screenshot specific element\n          const element = await page.waitForSelector(\"${selector}\", { timeout: 10000 });\n          screenshot = await element.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n        } else {\n          // Screenshot full page or viewport\n          screenshot = await page.screenshot({\n            encoding: 'base64',\n            fullPage: ${fullPage},\n            type: 'png',\n            quality: ${quality}\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            screenshot: screenshot,\n            selector: \"${selector}\",\n            fullPage: ${fullPage},\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced form filling with intelligent field detection\n   */ async fillForm(url, formData, options) {\n        const submitAfterFill = options?.submitAfterFill ?? false;\n        const waitForNavigation = options?.waitForNavigation ?? false;\n        const formSelector = options?.formSelector || 'form';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const formData = ${JSON.stringify(formData)};\n        const results = [];\n\n        // Wait for form to be present\n        await page.waitForSelector(\"${formSelector}\", { timeout: 10000 });\n\n        // Fill each field intelligently\n        for (const [fieldName, value] of Object.entries(formData)) {\n          try {\n            // Try multiple selector strategies\n            const selectors = [\n              \\`input[name=\"\\${fieldName}\"]\\`,\n              \\`input[id=\"\\${fieldName}\"]\\`,\n              \\`textarea[name=\"\\${fieldName}\"]\\`,\n              \\`select[name=\"\\${fieldName}\"]\\`,\n              \\`input[placeholder*=\"\\${fieldName}\"]\\`,\n              \\`input[aria-label*=\"\\${fieldName}\"]\\`,\n              \\`[data-testid=\"\\${fieldName}\"]\\`\n            ];\n\n            let filled = false;\n            for (const selector of selectors) {\n              const elements = await page.$$(selector);\n              if (elements.length > 0) {\n                const element = elements[0];\n                const tagName = await element.evaluate(el => el.tagName.toLowerCase());\n\n                if (tagName === 'select') {\n                  await element.selectOption(value.toString());\n                } else if (tagName === 'input') {\n                  const inputType = await element.getAttribute('type');\n                  if (inputType === 'checkbox' || inputType === 'radio') {\n                    if (value) await element.check();\n                  } else {\n                    await element.fill(value.toString());\n                  }\n                } else {\n                  await element.fill(value.toString());\n                }\n\n                results.push({\n                  field: fieldName,\n                  selector: selector,\n                  value: value,\n                  success: true\n                });\n                filled = true;\n                break;\n              }\n            }\n\n            if (!filled) {\n              results.push({\n                field: fieldName,\n                value: value,\n                success: false,\n                error: 'Field not found'\n              });\n            }\n          } catch (error) {\n            results.push({\n              field: fieldName,\n              value: value,\n              success: false,\n              error: error.message\n            });\n          }\n        }\n\n        let submitResult = null;\n        if (${submitAfterFill}) {\n          try {\n            const submitButton = await page.$('input[type=\"submit\"], button[type=\"submit\"], button:has-text(\"Submit\")');\n            if (submitButton) {\n              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}\n              submitResult = { success: true, message: 'Form submitted successfully' };\n            } else {\n              submitResult = { success: false, error: 'Submit button not found' };\n            }\n          } catch (error) {\n            submitResult = { success: false, error: error.message };\n          }\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            formFillResults: results,\n            submitResult: submitResult,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * CAPTCHA solving with multiple strategies\n   */ async solveCaptcha(url, captchaType = 'recaptcha') {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const captchaType = \"${captchaType}\";\n        let result = { success: false, type: captchaType };\n\n        try {\n          if (captchaType === 'recaptcha') {\n            // Look for reCAPTCHA\n            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');\n            if (recaptcha) {\n              // For now, we'll detect and report the presence\n              // In production, integrate with 2captcha or similar service\n              const sitekey = await recaptcha.getAttribute('data-sitekey');\n              result = {\n                success: false,\n                type: 'recaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'reCAPTCHA detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'hcaptcha') {\n            // Look for hCaptcha\n            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');\n            if (hcaptcha) {\n              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');\n              result = {\n                success: false,\n                type: 'hcaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'hCaptcha detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'text') {\n            // Look for text-based CAPTCHA\n            const textCaptcha = await page.$('img[src*=\"captcha\"], img[alt*=\"captcha\"], .captcha-image');\n            if (textCaptcha) {\n              result = {\n                success: false,\n                type: 'text',\n                detected: true,\n                message: 'Text CAPTCHA detected but solving not implemented yet'\n              };\n            }\n          }\n\n          // If no CAPTCHA detected\n          if (!result.detected) {\n            result = {\n              success: true,\n              type: captchaType,\n              detected: false,\n              message: 'No CAPTCHA detected on page'\n            };\n          }\n        } catch (error) {\n          result = {\n            success: false,\n            type: captchaType,\n            error: error.message\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            captchaResult: result,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Execute custom JavaScript with advanced capabilities\n   */ async executeAdvancedScript(url, script, options) {\n        const waitForSelector = options?.waitForSelector || '';\n        const timeout = options?.timeout || 30000;\n        const returnType = options?.returnType || 'json';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        ${waitForSelector ? `await page.waitForSelector(\"${waitForSelector}\", { timeout: ${timeout} });` : ''}\n\n        // Execute custom script\n        const scriptResult = await page.evaluate(() => {\n          ${script}\n        });\n\n        let finalResult = scriptResult;\n\n        if (\"${returnType}\" === 'screenshot') {\n          const screenshot = await page.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n          finalResult = {\n            scriptResult: scriptResult,\n            screenshot: screenshot\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            result: finalResult,\n            returnType: \"${returnType}\",\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Smart content extraction with multiple strategies\n   */ async smartExtract(url, extractionGoals) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const goals = ${JSON.stringify(extractionGoals)};\n        const results = {};\n\n        // Common extraction patterns\n        const extractors = {\n          prices: () => {\n            const priceSelectors = [\n              '[class*=\"price\"]', '[id*=\"price\"]', '.cost', '.amount',\n              '[data-testid*=\"price\"]', '.currency', '[class*=\"dollar\"]'\n            ];\n            const prices = [];\n            priceSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const text = el.textContent?.trim();\n                if (text && /[$£€¥₹]|\\\\d+\\\\.\\\\d{2}/.test(text)) {\n                  prices.push({\n                    text: text,\n                    selector: selector,\n                    element: el.tagName\n                  });\n                }\n              });\n            });\n            return prices;\n          },\n\n          contact: () => {\n            const contactSelectors = [\n              '[href^=\"mailto:\"]', '[href^=\"tel:\"]', '.contact', '.email', '.phone'\n            ];\n            const contacts = [];\n            contactSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                contacts.push({\n                  text: el.textContent?.trim(),\n                  href: el.getAttribute('href'),\n                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'\n                });\n              });\n            });\n            return contacts;\n          },\n\n          products: () => {\n            const productSelectors = [\n              '.product', '[class*=\"product\"]', '.item', '[data-testid*=\"product\"]'\n            ];\n            const products = [];\n            productSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const title = el.querySelector('h1, h2, h3, .title, [class*=\"title\"]')?.textContent?.trim();\n                const price = el.querySelector('[class*=\"price\"], .cost')?.textContent?.trim();\n                const image = el.querySelector('img')?.src;\n                if (title) {\n                  products.push({ title, price, image });\n                }\n              });\n            });\n            return products;\n          },\n\n          text: () => {\n            // Extract main content\n            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];\n            let content = '';\n            for (const selector of contentSelectors) {\n              const el = document.querySelector(selector);\n              if (el) {\n                content = el.textContent?.trim() || '';\n                break;\n              }\n            }\n            if (!content) {\n              content = document.body.textContent?.trim() || '';\n            }\n            return content.substring(0, 5000); // Limit to 5000 chars\n          },\n\n          links: () => {\n            const links = [];\n            document.querySelectorAll('a[href]').forEach(el => {\n              const href = el.getAttribute('href');\n              const text = el.textContent?.trim();\n              if (href && text && !href.startsWith('#')) {\n                links.push({\n                  url: new URL(href, window.location.href).href,\n                  text: text\n                });\n              }\n            });\n            return links.slice(0, 50); // Limit to 50 links\n          }\n        };\n\n        // Execute extractors based on goals\n        goals.forEach(goal => {\n          const goalLower = goal.toLowerCase();\n          if (goalLower.includes('price') || goalLower.includes('cost')) {\n            results.prices = extractors.prices();\n          }\n          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {\n            results.contact = extractors.contact();\n          }\n          if (goalLower.includes('product') || goalLower.includes('item')) {\n            results.products = extractors.products();\n          }\n          if (goalLower.includes('text') || goalLower.includes('content')) {\n            results.text = extractors.text();\n          }\n          if (goalLower.includes('link') || goalLower.includes('url')) {\n            results.links = extractors.links();\n          }\n        });\n\n        // If no specific goals, extract everything\n        if (goals.length === 0) {\n          Object.keys(extractors).forEach(key => {\n            results[key] = extractors[key]();\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            extractionGoals: goals,\n            results: results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    // Get service statistics\n    getStats() {\n        return {\n            totalKeys: this.apiKeys.length,\n            healthyKeys: this.getHealthyKeys().length,\n            keyUsage: Object.fromEntries(this.keyUsageCount),\n            keyErrors: Object.fromEntries(this.keyErrors)\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowserlessService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browserless.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/BrowsingExecutionService.ts":
/*!******************************************************!*\
  !*** ./src/lib/browsing/BrowsingExecutionService.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrowsingExecutionService: () => (/* binding */ BrowsingExecutionService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n/* harmony import */ var _SmartBrowsingExecutor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SmartBrowsingExecutor */ \"(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts\");\n// Browsing Execution Service - Handles browsing model selection with fallback support\n// Integrates with BrowserlessService for actual web browsing\n\n\nclass BrowsingExecutionService {\n    constructor(){\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n        this.smartBrowsingExecutor = _SmartBrowsingExecutor__WEBPACK_IMPORTED_MODULE_1__.SmartBrowsingExecutor.getInstance();\n    }\n    static getInstance() {\n        if (!BrowsingExecutionService.instance) {\n            BrowsingExecutionService.instance = new BrowsingExecutionService();\n        }\n        return BrowsingExecutionService.instance;\n    }\n    /**\n   * Execute browsing with model fallback support\n   * Now uses SmartBrowsingExecutor for intelligent, plan-based browsing\n   */ async executeBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery, useSmartBrowsing = true, progressCallback) {\n        try {\n            if (!browsingConfig.browsing_enabled) {\n                return {\n                    success: false,\n                    error: 'Browsing is not enabled for this configuration'\n                };\n            }\n            if (!browsingConfig.browsing_models || browsingConfig.browsing_models.length === 0) {\n                return {\n                    success: false,\n                    error: 'No browsing models configured'\n                };\n            }\n            console.log(`[Browsing Execution] Starting ${useSmartBrowsing ? 'SMART' : 'SIMPLE'} browsing`);\n            console.log(`[Browsing Execution] Query: \"${query}\", Type: ${browsingType}`);\n            // Use Smart Browsing for complex tasks\n            if (useSmartBrowsing) {\n                console.log(`[Browsing Execution] 🧠 Using Smart Browsing Executor`);\n                const smartResult = await this.smartBrowsingExecutor.executeSmartBrowsing(refinedQuery || query, browsingConfig, browsingType, progressCallback);\n                if (smartResult.success) {\n                    return {\n                        success: true,\n                        content: smartResult.content,\n                        modelUsed: browsingConfig.browsing_models[0]?.model || 'smart-browsing',\n                        providerUsed: browsingConfig.browsing_models[0]?.provider || 'smart-browsing',\n                        browsingData: smartResult.plan\n                    };\n                } else {\n                    // Check if we should fallback due to network issues\n                    if (smartResult.shouldFallback) {\n                        console.log(`[Browsing Execution] 🌐 Network issue detected, falling back to simple browsing: ${smartResult.error}`);\n                    } else {\n                        console.log(`[Browsing Execution] Smart browsing failed, falling back to simple browsing: ${smartResult.error}`);\n                    }\n                // Fall back to simple browsing\n                }\n            }\n            // Fallback to simple browsing (original logic)\n            console.log(`[Browsing Execution] 🔄 Using Simple Browsing (fallback)`);\n            return await this.executeSimpleBrowsing(query, browsingConfig, browsingType, refinedQuery);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Fatal error:', errorMessage);\n            return {\n                success: false,\n                error: `Browsing execution failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Execute simple browsing (original logic) as fallback\n   */ async executeSimpleBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery) {\n        // Sort models by order for fallback\n        const sortedModels = [\n            ...browsingConfig.browsing_models\n        ].sort((a, b)=>a.order - b.order);\n        console.log(`[Simple Browsing] Starting with ${sortedModels.length} models configured`);\n        let lastError = null;\n        // Try each model in order (strict fallback pattern)\n        for (const model of sortedModels){\n            try {\n                console.log(`[Simple Browsing] Attempting with ${model.provider}/${model.model}`);\n                // First, perform the web browsing\n                const browsingResult = await this.performWebBrowsing(refinedQuery || query, browsingType);\n                if (!browsingResult.success) {\n                    throw new Error(browsingResult.error || 'Browsing failed');\n                }\n                console.log(`[Simple Browsing] ✅ Web browsing successful, got ${JSON.stringify(browsingResult.data).length} characters of data`);\n                // Then, use the AI model to process the browsing results\n                const aiResult = await this.processWithAI(query, browsingResult.data, model, browsingType);\n                if (aiResult.success) {\n                    console.log(`[Simple Browsing] ✅ Success with ${model.provider}/${model.model}`);\n                    return {\n                        success: true,\n                        content: aiResult.content,\n                        modelUsed: model.model,\n                        providerUsed: model.provider,\n                        browsingData: browsingResult.data\n                    };\n                } else {\n                    throw new Error(aiResult.error || 'AI processing failed');\n                }\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n                lastError = errorMessage;\n                console.log(`[Simple Browsing] ❌ Failed with ${model.provider}/${model.model}: ${errorMessage}`);\n                continue;\n            }\n        }\n        // If we get here, all models failed\n        return {\n            success: false,\n            error: `All browsing models failed. Last error: ${lastError}`\n        };\n    }\n    /**\n   * Perform web browsing using BrowserlessService\n   */ async performWebBrowsing(query, browsingType) {\n        try {\n            let result;\n            switch(browsingType){\n                case 'search':\n                    // Use search functionality\n                    result = await this.browserlessService.searchAndExtractUnblocked(query);\n                    break;\n                case 'navigate':\n                    // Try to extract URL from query or use as-is\n                    const urlMatch = query.match(/https?:\\/\\/[^\\s]+/);\n                    const url = urlMatch ? urlMatch[0] : query;\n                    result = await this.browserlessService.navigateAndExtract(url);\n                    break;\n                case 'extract':\n                    // Similar to navigate but with specific extraction\n                    const extractUrl = query.match(/https?:\\/\\/[^\\s]+/)?.[0] || query;\n                    result = await this.browserlessService.navigateAndExtract(extractUrl);\n                    break;\n                default:\n                    // Default to search\n                    result = await this.browserlessService.searchAndExtractUnblocked(query);\n            }\n            if (result && result.data) {\n                console.log(`[Browsing Execution] ✅ Web browsing successful, got ${JSON.stringify(result.data).length} characters of data`);\n                return {\n                    success: true,\n                    data: result.data\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No data returned from browsing'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Web browsing failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Process browsing results with AI model\n   */ async processWithAI(originalQuery, browsingData, model, browsingType) {\n        try {\n            const prompt = this.buildProcessingPrompt(originalQuery, browsingData, browsingType);\n            // Call the appropriate AI provider\n            const response = await this.callAIProvider(prompt, model);\n            if (response && response.content) {\n                return {\n                    success: true,\n                    content: response.content\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No content returned from AI model'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Build processing prompt for AI model\n   */ buildProcessingPrompt(originalQuery, browsingData, browsingType) {\n        const dataStr = JSON.stringify(browsingData, null, 2);\n        return `You are an AI assistant that processes web browsing results to answer user queries.\n\nUSER QUERY: \"${originalQuery}\"\nBROWSING TYPE: ${browsingType}\n\nWEB BROWSING RESULTS:\n${dataStr}\n\nINSTRUCTIONS:\n1. Analyze the browsing results carefully\n2. Extract relevant information that answers the user's query\n3. Provide a comprehensive, well-structured response\n4. If the browsing results don't contain relevant information, say so clearly\n5. Include specific details, numbers, dates, and facts from the browsing results\n6. Organize the information in a clear, readable format\n7. Cite sources when possible (URLs, website names, etc.)\n\nPlease provide a helpful response based on the browsing results:`;\n    }\n    /**\n   * Get the correct model ID for API calls (following RouKey's pattern)\n   * OpenRouter keeps full model ID, other providers strip the prefix\n   */ getEffectiveModelId(model) {\n        // For OpenRouter, return the full model ID\n        if (model.provider.toLowerCase() === 'openrouter') {\n            return model.model;\n        }\n        // For other providers, extract the model name after the prefix\n        const parts = model.model.split('/');\n        return parts.length > 1 ? parts[parts.length - 1] : model.model;\n    }\n    /**\n   * Call AI provider based on model configuration\n   */ async callAIProvider(prompt, model) {\n        try {\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            // Get the effective model ID following RouKey's pattern\n            const effectiveModelId = this.getEffectiveModelId(model);\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'openrouter':\n                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`,\n                        'HTTP-Referer': 'https://roukey.online',\n                        'X-Title': 'RouKey'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider: ${model.provider}`);\n            }\n            console.log(`[Browsing AI] Calling ${model.provider} API with model ${effectiveModelId} (original: ${model.model})...`);\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000) // 30s timeout\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(`[Browsing AI] API error: ${response.status} - ${errorText}`);\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(`[Browsing AI] Raw response:`, JSON.stringify(result, null, 2));\n            // Extract content based on provider response format\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                // For OpenAI-compatible APIs (including Google's OpenAI-compatible endpoint)\n                content = result.choices?.[0]?.message?.content;\n            }\n            console.log(`[Browsing AI] Extracted content length: ${content?.length || 0}`);\n            if (!content || content.trim().length === 0) {\n                console.error(`[Browsing AI] No content extracted from response. Full response:`, result);\n                return {\n                    error: 'No content returned from AI model - empty response'\n                };\n            }\n            return {\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error(`[Browsing AI] Error calling AI provider:`, errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowsingExecutionService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/BrowsingExecutionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts":
/*!***************************************************!*\
  !*** ./src/lib/browsing/SmartBrowsingExecutor.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartBrowsingExecutor: () => (/* binding */ SmartBrowsingExecutor)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n// Smart Browsing Executor - Intelligent plan-based browsing with todo list management\n// Handles complex browsing tasks by creating plans, executing subtasks, and updating progress\n\nclass SmartBrowsingExecutor {\n    constructor(){\n        this.activePlans = new Map();\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n    }\n    static getInstance() {\n        if (!SmartBrowsingExecutor.instance) {\n            SmartBrowsingExecutor.instance = new SmartBrowsingExecutor();\n        }\n        return SmartBrowsingExecutor.instance;\n    }\n    /**\n   * Execute smart browsing with planning and todo list management\n   */ async executeSmartBrowsing(query, browsingConfig, browsingType = 'search', progressCallback) {\n        try {\n            console.log(`[Smart Browsing] 🎯 Starting intelligent browsing for: \"${query}\"`);\n            // Step 1: Create a browsing plan\n            const plan = await this.createBrowsingPlan(query, browsingType, browsingConfig);\n            this.activePlans.set(plan.id, plan);\n            console.log(`[Smart Browsing] 📋 Created plan with ${plan.subtasks.length} subtasks`);\n            this.logPlan(plan);\n            // Notify plan creation\n            progressCallback?.onPlanCreated?.(plan);\n            // Step 2: Execute the plan\n            const result = await this.executePlan(plan, browsingConfig, progressCallback);\n            if (result.success) {\n                console.log(`[Smart Browsing] ✅ Plan completed successfully`);\n                return {\n                    success: true,\n                    content: result.content,\n                    plan: plan\n                };\n            } else {\n                console.log(`[Smart Browsing] ❌ Plan failed: ${result.error}`);\n                // Check if this is a network connectivity issue\n                if (this.isNetworkError(result.error)) {\n                    console.log(`[Smart Browsing] 🌐 Network connectivity issue detected, recommending fallback to simple browsing`);\n                    return {\n                        success: false,\n                        error: `Network connectivity issue: ${result.error}. Falling back to simple browsing.`,\n                        plan: plan,\n                        shouldFallback: true\n                    };\n                }\n                return {\n                    success: false,\n                    error: result.error,\n                    plan: plan\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Smart Browsing] Fatal error:', errorMessage);\n            // Check if this is a network connectivity issue\n            if (this.isNetworkError(errorMessage)) {\n                console.log(`[Smart Browsing] 🌐 Network connectivity issue detected, recommending fallback to simple browsing`);\n                return {\n                    success: false,\n                    error: `Network connectivity issue: ${errorMessage}. Falling back to simple browsing.`,\n                    shouldFallback: true\n                };\n            }\n            return {\n                success: false,\n                error: `Smart browsing failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Check if an error is related to network connectivity\n   */ isNetworkError(errorMessage) {\n        const networkErrorPatterns = [\n            'fetch failed',\n            'ECONNRESET',\n            'ENOTFOUND',\n            'ETIMEDOUT',\n            'ECONNREFUSED',\n            'Network request failed',\n            'Connection timeout',\n            'DNS resolution failed'\n        ];\n        return networkErrorPatterns.some((pattern)=>errorMessage.toLowerCase().includes(pattern.toLowerCase()));\n    }\n    /**\n   * Create an intelligent browsing plan based on the query\n   */ async createBrowsingPlan(query, browsingType, browsingConfig) {\n        const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        // Use AI to create a smart plan\n        const planningResult = await this.generatePlanWithAI(query, browsingType, browsingConfig);\n        const plan = {\n            id: planId,\n            originalQuery: query,\n            goal: planningResult.goal || `Find comprehensive information about: ${query}`,\n            subtasks: planningResult.subtasks || this.createFallbackPlan(query, browsingType),\n            status: 'planning',\n            progress: 0,\n            gatheredData: {},\n            visitedUrls: [],\n            searchQueries: [],\n            completedSubtasks: [],\n            failedSubtasks: [],\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        return plan;\n    }\n    /**\n   * Generate a browsing plan using AI\n   */ async generatePlanWithAI(query, browsingType, browsingConfig) {\n        try {\n            const model = browsingConfig.browsing_models[0]; // Use first available model for planning\n            if (!model) {\n                throw new Error('No browsing models available for planning');\n            }\n            const planningPrompt = this.buildPlanningPrompt(query, browsingType);\n            const aiResult = await this.callAIForPlanning(planningPrompt, model);\n            if (aiResult.success && aiResult.content) {\n                return this.parsePlanFromAI(aiResult.content, query);\n            } else {\n                console.warn('[Smart Browsing] AI planning failed, using fallback plan');\n                return {\n                    goal: `Find information about: ${query}`,\n                    subtasks: this.createFallbackPlan(query, browsingType)\n                };\n            }\n        } catch (error) {\n            console.warn('[Smart Browsing] AI planning error, using fallback:', error);\n            return {\n                goal: `Find information about: ${query}`,\n                subtasks: this.createFallbackPlan(query, browsingType)\n            };\n        }\n    }\n    /**\n   * Build a comprehensive planning prompt for AI\n   */ buildPlanningPrompt(query, browsingType) {\n        // Get current date and time for context\n        const now = new Date();\n        const currentDateTime = now.toLocaleString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit',\n            timeZoneName: 'short'\n        });\n        const currentYear = now.getFullYear();\n        const currentMonth = now.toLocaleString('en-US', {\n            month: 'long'\n        });\n        return `You are an expert web browsing strategist. Create a detailed browsing plan to thoroughly research this query: \"${query}\"\n\nCURRENT DATE & TIME: ${currentDateTime}\nCURRENT YEAR: ${currentYear}\nCURRENT MONTH: ${currentMonth}\n\nBROWSING TYPE: ${browsingType}\n\nIMPORTANT: When generating search terms and subtasks, consider the current date and time context:\n- For recent events, include \"${currentYear}\" in search terms\n- For current trends, use \"latest\", \"recent\", \"${currentMonth} ${currentYear}\"\n- For news queries, prioritize recent timeframes\n- For technology/AI topics, include current year for latest developments\n- For market/business queries, focus on current and recent data\n\nCreate a JSON response with this structure:\n{\n  \"goal\": \"Clear statement of what we want to achieve\",\n  \"subtasks\": [\n    {\n      \"id\": \"unique_id\",\n      \"type\": \"search|navigate|extract|analyze\",\n      \"description\": \"What this subtask does\",\n      \"query\": \"Specific search query or URL\",\n      \"priority\": 1-10,\n      \"searchTerms\": [\"alternative\", \"search\", \"terms\"],\n      \"expectedInfo\": \"What information we expect to find\"\n    }\n  ]\n}\n\nGUIDELINES:\n1. Start with broad searches, then get more specific\n2. Use multiple search strategies and terms with temporal context\n3. Include fact-checking and verification steps\n4. Plan for 3-7 subtasks maximum\n5. Make search terms diverse, comprehensive, and time-aware\n6. Consider different angles and perspectives\n7. Include analysis steps to synthesize information\n8. For \"navigate\" tasks, only use if you have specific URLs (https://...)\n9. Most tasks should be \"search\" type for better reliability\n10. ALWAYS include temporal keywords when relevant:\n    - For news: \"latest news\", \"recent updates\", \"${currentMonth} ${currentYear}\"\n    - For trends: \"current trends\", \"${currentYear} trends\"\n    - For technology: \"latest developments\", \"${currentYear} updates\"\n    - For data/statistics: \"current data\", \"recent statistics\", \"${currentYear} data\"\n\nCreate a smart, thorough, and temporally-aware plan:`;\n    }\n    /**\n   * Enhance query with temporal context for better search results\n   */ enhanceQueryWithTemporal(query, temporalKeywords) {\n        const currentYear = new Date().getFullYear();\n        const currentMonth = new Date().toLocaleString('en-US', {\n            month: 'long'\n        });\n        // Detect if query already has temporal context\n        const hasTemporalContext = /\\b(latest|recent|current|new|today|\\d{4}|now)\\b/i.test(query);\n        return {\n            primary: query,\n            temporal: hasTemporalContext ? query : `${query} ${currentYear} latest`,\n            alternatives: [\n                query,\n                `${query} information`,\n                `${query} details`,\n                hasTemporalContext ? `${query} overview` : `${query} ${currentYear}`\n            ],\n            recentTerms: [\n                `${query} recent`,\n                `${query} latest news`,\n                `${query} ${currentMonth} ${currentYear}`,\n                `${query} current trends`\n            ]\n        };\n    }\n    /**\n   * Create a fallback plan when AI planning fails\n   */ createFallbackPlan(query, browsingType) {\n        const baseId = Date.now();\n        const currentYear = new Date().getFullYear();\n        const currentMonth = new Date().toLocaleString('en-US', {\n            month: 'long'\n        });\n        // Add temporal context to search terms\n        const temporalKeywords = [\n            `${currentYear}`,\n            `latest`,\n            `recent`,\n            `${currentMonth} ${currentYear}`\n        ];\n        const enhancedQuery = this.enhanceQueryWithTemporal(query, temporalKeywords);\n        return [\n            {\n                id: `search_${baseId}_1`,\n                type: 'search',\n                description: 'Primary search for main topic',\n                query: enhancedQuery.primary,\n                status: 'pending',\n                priority: 10,\n                attempts: 0,\n                maxAttempts: 3,\n                searchTerms: enhancedQuery.alternatives,\n                expectedInfo: 'General information about the topic'\n            },\n            {\n                id: `search_${baseId}_2`,\n                type: 'search',\n                description: 'Secondary search with temporal context',\n                query: enhancedQuery.temporal,\n                status: 'pending',\n                priority: 8,\n                attempts: 0,\n                maxAttempts: 3,\n                searchTerms: enhancedQuery.recentTerms,\n                expectedInfo: 'Recent developments and current information'\n            },\n            {\n                id: `analyze_${baseId}`,\n                type: 'analyze',\n                description: 'Analyze and synthesize gathered information',\n                query: 'synthesize findings',\n                status: 'pending',\n                priority: 5,\n                attempts: 0,\n                maxAttempts: 1,\n                dependencies: [\n                    `search_${baseId}_1`,\n                    `search_${baseId}_2`\n                ],\n                expectedInfo: 'Comprehensive summary of findings'\n            }\n        ];\n    }\n    /**\n   * Execute the browsing plan step by step\n   */ async executePlan(plan, browsingConfig, progressCallback) {\n        plan.status = 'executing';\n        plan.updatedAt = new Date().toISOString();\n        console.log(`[Smart Browsing] 🚀 Starting plan execution`);\n        try {\n            // Execute subtasks in priority order, respecting dependencies\n            while(this.hasRemainingTasks(plan)){\n                const nextTask = this.getNextExecutableTask(plan);\n                if (!nextTask) {\n                    console.log(`[Smart Browsing] ⏸️ No executable tasks remaining, checking if plan is complete`);\n                    break;\n                }\n                console.log(`[Smart Browsing] 🔄 Executing subtask: ${nextTask.description}`);\n                plan.currentSubtask = nextTask.id;\n                nextTask.status = 'in_progress';\n                // Notify task started\n                progressCallback?.onTaskStarted?.(nextTask, plan);\n                progressCallback?.onStatusUpdate?.(`Executing: ${nextTask.description}`, plan);\n                const taskResult = await this.executeSubtask(nextTask, plan, browsingConfig);\n                if (taskResult.success) {\n                    nextTask.status = 'completed';\n                    nextTask.result = taskResult.data;\n                    plan.completedSubtasks.push(nextTask.id);\n                    console.log(`[Smart Browsing] ✅ Subtask completed: ${nextTask.description}`);\n                    // Notify task completed\n                    progressCallback?.onTaskCompleted?.(nextTask, plan);\n                } else {\n                    nextTask.attempts++;\n                    nextTask.error = taskResult.error;\n                    if (nextTask.attempts >= nextTask.maxAttempts) {\n                        nextTask.status = 'failed';\n                        plan.failedSubtasks.push(nextTask.id);\n                        console.log(`[Smart Browsing] ❌ Subtask failed permanently: ${nextTask.description}`);\n                        // Notify task failed\n                        progressCallback?.onTaskFailed?.(nextTask, plan);\n                    } else {\n                        nextTask.status = 'pending';\n                        console.log(`[Smart Browsing] 🔄 Subtask failed, will retry (${nextTask.attempts}/${nextTask.maxAttempts}): ${nextTask.description}`);\n                    }\n                }\n                // Update progress\n                plan.progress = this.calculateProgress(plan);\n                progressCallback?.onProgressUpdate?.(plan.progress, plan);\n                plan.updatedAt = new Date().toISOString();\n                this.logProgress(plan);\n            }\n            // Generate final result\n            const finalResult = await this.synthesizeFinalResult(plan, browsingConfig);\n            plan.finalResult = finalResult;\n            plan.status = 'completed';\n            plan.progress = 100;\n            // Notify plan completed\n            progressCallback?.onPlanCompleted?.(plan);\n            progressCallback?.onStatusUpdate?.('Browsing completed successfully!', plan);\n            return {\n                success: true,\n                content: finalResult\n            };\n        } catch (error) {\n            plan.status = 'failed';\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Smart Browsing] Plan execution failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Check if there are remaining tasks to execute\n   */ hasRemainingTasks(plan) {\n        return plan.subtasks.some((task)=>task.status === 'pending' || task.status === 'in_progress');\n    }\n    /**\n   * Get the next executable task (highest priority, dependencies met)\n   */ getNextExecutableTask(plan) {\n        const executableTasks = plan.subtasks.filter((task)=>{\n            if (task.status !== 'pending') return false;\n            // Check if dependencies are met\n            if (task.dependencies && task.dependencies.length > 0) {\n                return task.dependencies.every((depId)=>plan.completedSubtasks.includes(depId));\n            }\n            return true;\n        });\n        // Sort by priority (highest first)\n        executableTasks.sort((a, b)=>b.priority - a.priority);\n        return executableTasks[0] || null;\n    }\n    /**\n   * Execute a single subtask\n   */ async executeSubtask(subtask, plan, browsingConfig) {\n        try {\n            switch(subtask.type){\n                case 'search':\n                    return await this.executeSearchSubtask(subtask, plan);\n                case 'navigate':\n                    return await this.executeNavigateSubtask(subtask, plan);\n                case 'extract':\n                    return await this.executeExtractSubtask(subtask, plan);\n                case 'analyze':\n                    return await this.executeAnalyzeSubtask(subtask, plan, browsingConfig);\n                default:\n                    throw new Error(`Unknown subtask type: ${subtask.type}`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Execute a search subtask with smart query refinement\n   */ async executeSearchSubtask(subtask, plan) {\n        const searchTerms = [\n            subtask.query,\n            ...subtask.searchTerms || []\n        ];\n        let lastError = '';\n        // Try different search terms until we get good results\n        for (const searchTerm of searchTerms){\n            try {\n                console.log(`[Smart Browsing] 🔍 Searching for: \"${searchTerm}\"`);\n                plan.searchQueries.push(searchTerm);\n                const result = await this.browserlessService.searchAndExtractUnblocked(searchTerm);\n                if (result.data && result.data.results && result.data.results.length > 0) {\n                    console.log(`[Smart Browsing] ✅ Found ${result.data.results.length} results for: \"${searchTerm}\"`);\n                    // Store URLs we've found\n                    result.data.results.forEach((item)=>{\n                        if (item.link && !plan.visitedUrls.includes(item.link)) {\n                            plan.visitedUrls.push(item.link);\n                        }\n                    });\n                    return {\n                        success: true,\n                        data: result.data\n                    };\n                } else {\n                    lastError = `No results found for: \"${searchTerm}\"`;\n                    console.log(`[Smart Browsing] ⚠️ ${lastError}`);\n                }\n            } catch (error) {\n                lastError = error instanceof Error ? error.message : 'Search failed';\n                console.log(`[Smart Browsing] ❌ Search error for \"${searchTerm}\": ${lastError}`);\n            }\n        }\n        return {\n            success: false,\n            error: `All search terms failed. Last error: ${lastError}`\n        };\n    }\n    /**\n   * Execute navigate subtask\n   */ async executeNavigateSubtask(subtask, plan) {\n        try {\n            const query = subtask.query;\n            console.log(`[Smart Browsing] 🌐 Processing navigation task: ${query}`);\n            // Check if the query is a valid URL\n            const urlPattern = /^https?:\\/\\//i;\n            if (urlPattern.test(query)) {\n                // Direct URL navigation\n                console.log(`[Smart Browsing] 🌐 Navigating to URL: ${query}`);\n                if (!plan.visitedUrls.includes(query)) {\n                    plan.visitedUrls.push(query);\n                }\n                const result = await this.browserlessService.navigateAndExtract(query);\n                if (result.data) {\n                    return {\n                        success: true,\n                        data: result.data\n                    };\n                } else {\n                    return {\n                        success: false,\n                        error: 'No data extracted from navigation'\n                    };\n                }\n            } else {\n                // Not a direct URL - convert to search task\n                console.log(`[Smart Browsing] 🔄 Converting navigation to search: ${query}`);\n                // Extract meaningful search terms from the navigation description\n                let searchQuery = query;\n                if (query.toLowerCase().includes('navigate to')) {\n                    searchQuery = query.replace(/navigate to\\s*/i, '').trim();\n                }\n                if (query.toLowerCase().includes('websites of')) {\n                    searchQuery = searchQuery.replace(/websites of\\s*/i, '').trim();\n                }\n                // Use the search functionality instead\n                return await this.executeSearchSubtask({\n                    ...subtask,\n                    type: 'search',\n                    query: searchQuery\n                }, plan);\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Navigation failed'\n            };\n        }\n    }\n    /**\n   * Execute extract subtask\n   */ async executeExtractSubtask(subtask, plan) {\n        // Similar to navigate but with specific extraction focus\n        return this.executeNavigateSubtask(subtask, plan);\n    }\n    /**\n   * Execute analyze subtask - synthesize gathered information\n   */ async executeAnalyzeSubtask(subtask, plan, browsingConfig) {\n        try {\n            console.log(`[Smart Browsing] 🧠 Analyzing gathered data...`);\n            // Collect all data from completed subtasks\n            const gatheredData = plan.subtasks.filter((task)=>task.status === 'completed' && task.result).map((task)=>({\n                    type: task.type,\n                    description: task.description,\n                    query: task.query,\n                    data: task.result\n                }));\n            if (gatheredData.length === 0) {\n                return {\n                    success: false,\n                    error: 'No data available for analysis'\n                };\n            }\n            // Use AI to analyze and synthesize the data\n            const model = browsingConfig.browsing_models[0];\n            if (!model) {\n                return {\n                    success: false,\n                    error: 'No AI model available for analysis'\n                };\n            }\n            const analysisPrompt = this.buildAnalysisPrompt(plan.originalQuery, gatheredData);\n            const aiResult = await this.callAIForAnalysis(analysisPrompt, model);\n            if (aiResult.success && aiResult.content) {\n                return {\n                    success: true,\n                    data: {\n                        analysis: aiResult.content,\n                        sourceData: gatheredData\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    error: aiResult.error || 'Analysis failed'\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Analysis failed'\n            };\n        }\n    }\n    /**\n   * Calculate progress percentage\n   */ calculateProgress(plan) {\n        const totalTasks = plan.subtasks.length;\n        const completedTasks = plan.completedSubtasks.length;\n        return Math.round(completedTasks / totalTasks * 100);\n    }\n    /**\n   * Log progress update\n   */ logProgress(plan) {\n        const completed = plan.completedSubtasks.length;\n        const failed = plan.failedSubtasks.length;\n        const total = plan.subtasks.length;\n        const remaining = total - completed - failed;\n        console.log(`[Smart Browsing] 📊 Progress: ${plan.progress}% (${completed}/${total} completed, ${failed} failed, ${remaining} remaining)`);\n    }\n    /**\n   * Build analysis prompt for AI\n   */ buildAnalysisPrompt(originalQuery, gatheredData) {\n        // Get current date and time for context\n        const now = new Date();\n        const currentDateTime = now.toLocaleString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit',\n            timeZoneName: 'short'\n        });\n        const dataContext = gatheredData.map((item, index)=>`Source ${index + 1} (${item.type}): ${item.description}\\nQuery: ${item.query}\\nData: ${JSON.stringify(item.data, null, 2)}`).join('\\n\\n---\\n\\n');\n        return `You are an expert information analyst. Analyze the following browsing data and provide a comprehensive answer to the original query.\n\nCURRENT DATE & TIME: ${currentDateTime}\nORIGINAL QUERY: \"${originalQuery}\"\n\nGATHERED DATA:\n${dataContext}\n\nPlease provide:\n1. A comprehensive answer to the original query\n2. Key findings and insights with temporal relevance\n3. Any conflicting information found\n4. Confidence level in the findings\n5. Recommendations for further research if needed\n6. Note the recency and relevance of the information found\n\nIMPORTANT: Consider the current date when analyzing the data. Prioritize recent information and note if any data appears outdated. For time-sensitive queries, emphasize the most current findings.\n\nFormat your response as a clear, well-structured analysis that directly addresses the user's query with temporal awareness.`;\n    }\n    /**\n   * Call AI for planning\n   */ async callAIForPlanning(prompt, model) {\n        try {\n            const effectiveModelId = this.getEffectiveModelId(model);\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider (same as BrowsingExecutionService)\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'openrouter':\n                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`,\n                        'HTTP-Referer': 'https://roukey.online',\n                        'X-Title': 'RouKey'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider for planning: ${model.provider}`);\n            }\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                content = result.choices?.[0]?.message?.content;\n            }\n            if (!content) {\n                throw new Error('No content returned from AI model');\n            }\n            return {\n                success: true,\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Call AI for analysis (same as planning but different purpose)\n   */ async callAIForAnalysis(prompt, model) {\n        return this.callAIForPlanning(prompt, model); // Same implementation\n    }\n    /**\n   * Get effective model ID (same logic as BrowsingExecutionService)\n   */ getEffectiveModelId(model) {\n        if (model.provider.toLowerCase() === 'openrouter') {\n            return model.model;\n        }\n        const parts = model.model.split('/');\n        return parts.length > 1 ? parts[parts.length - 1] : model.model;\n    }\n    /**\n   * Parse plan from AI response\n   */ parsePlanFromAI(aiResponse, originalQuery) {\n        try {\n            // Try to extract JSON from the response\n            const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n            if (!jsonMatch) {\n                throw new Error('No JSON found in AI response');\n            }\n            const parsed = JSON.parse(jsonMatch[0]);\n            if (!parsed.goal || !parsed.subtasks || !Array.isArray(parsed.subtasks)) {\n                throw new Error('Invalid plan structure from AI');\n            }\n            // Convert AI subtasks to our format\n            const subtasks = parsed.subtasks.map((task, index)=>({\n                    id: task.id || `ai_task_${Date.now()}_${index}`,\n                    type: task.type || 'search',\n                    description: task.description || `Task ${index + 1}`,\n                    query: task.query || originalQuery,\n                    status: 'pending',\n                    priority: task.priority || 5,\n                    attempts: 0,\n                    maxAttempts: 3,\n                    searchTerms: task.searchTerms || [],\n                    expectedInfo: task.expectedInfo || ''\n                }));\n            return {\n                goal: parsed.goal,\n                subtasks\n            };\n        } catch (error) {\n            console.warn('[Smart Browsing] Failed to parse AI plan, using fallback:', error);\n            return {\n                goal: `Find information about: ${originalQuery}`,\n                subtasks: this.createFallbackPlan(originalQuery, 'search')\n            };\n        }\n    }\n    /**\n   * Synthesize final result from all gathered data\n   */ async synthesizeFinalResult(plan, browsingConfig) {\n        try {\n            // Find the analysis result if available\n            const analysisTask = plan.subtasks.find((task)=>task.type === 'analyze' && task.status === 'completed' && task.result);\n            if (analysisTask && analysisTask.result?.analysis) {\n                return analysisTask.result.analysis;\n            }\n            // If no analysis task, create a summary from all completed tasks\n            const completedTasks = plan.subtasks.filter((task)=>task.status === 'completed' && task.result);\n            if (completedTasks.length === 0) {\n                return `No information was successfully gathered for the query: \"${plan.originalQuery}\"`;\n            }\n            // Create a basic summary\n            let summary = `Based on browsing research for \"${plan.originalQuery}\":\\n\\n`;\n            completedTasks.forEach((task, index)=>{\n                summary += `${index + 1}. ${task.description}:\\n`;\n                if (task.result?.results && Array.isArray(task.result.results)) {\n                    task.result.results.slice(0, 3).forEach((result)=>{\n                        summary += `   • ${result.title || 'Result'}\\n`;\n                    });\n                } else if (typeof task.result === 'string') {\n                    summary += `   ${task.result.substring(0, 200)}...\\n`;\n                }\n                summary += '\\n';\n            });\n            return summary;\n        } catch (error) {\n            console.error('[Smart Browsing] Error synthesizing final result:', error);\n            return `Research completed for \"${plan.originalQuery}\" but encountered errors in synthesis. Please check the individual results.`;\n        }\n    }\n    /**\n   * Log the browsing plan for debugging\n   */ logPlan(plan) {\n        console.log(`[Smart Browsing] 📋 BROWSING PLAN:`);\n        console.log(`[Smart Browsing] Goal: ${plan.goal}`);\n        console.log(`[Smart Browsing] Subtasks:`);\n        plan.subtasks.forEach((subtask, index)=>{\n            console.log(`[Smart Browsing]   ${index + 1}. [${subtask.type.toUpperCase()}] ${subtask.description}`);\n            console.log(`[Smart Browsing]      Query: \"${subtask.query}\"`);\n            console.log(`[Smart Browsing]      Priority: ${subtask.priority}, Status: ${subtask.status}`);\n            if (subtask.searchTerms && subtask.searchTerms.length > 0) {\n                console.log(`[Smart Browsing]      Alt terms: ${subtask.searchTerms.join(', ')}`);\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2Jyb3dzaW5nL1NtYXJ0QnJvd3NpbmdFeGVjdXRvci50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLHNGQUFzRjtBQUN0Riw4RkFBOEY7QUFFM0M7QUFvRDVDLE1BQU1DO0lBS1hDLGFBQWM7YUFGTkMsY0FBeUMsSUFBSUM7UUFHbkQsSUFBSSxDQUFDQyxrQkFBa0IsR0FBR0wsd0RBQWtCQSxDQUFDTSxXQUFXO0lBQzFEO0lBRUEsT0FBT0EsY0FBcUM7UUFDMUMsSUFBSSxDQUFDTCxzQkFBc0JNLFFBQVEsRUFBRTtZQUNuQ04sc0JBQXNCTSxRQUFRLEdBQUcsSUFBSU47UUFDdkM7UUFDQSxPQUFPQSxzQkFBc0JNLFFBQVE7SUFDdkM7SUFFQTs7R0FFQyxHQUNELE1BQU1DLHFCQUNKQyxLQUFhLEVBQ2JDLGNBQThCLEVBQzlCQyxlQUFrRCxRQUFRLEVBQzFEQyxnQkFBMkMsRUFDMkM7UUFDdEYsSUFBSTtZQUNGQyxRQUFRQyxHQUFHLENBQUMsQ0FBQyx3REFBd0QsRUFBRUwsTUFBTSxDQUFDLENBQUM7WUFFL0UsaUNBQWlDO1lBQ2pDLE1BQU1NLE9BQU8sTUFBTSxJQUFJLENBQUNDLGtCQUFrQixDQUFDUCxPQUFPRSxjQUFjRDtZQUNoRSxJQUFJLENBQUNQLFdBQVcsQ0FBQ2MsR0FBRyxDQUFDRixLQUFLRyxFQUFFLEVBQUVIO1lBRTlCRixRQUFRQyxHQUFHLENBQUMsQ0FBQyxzQ0FBc0MsRUFBRUMsS0FBS0ksUUFBUSxDQUFDQyxNQUFNLENBQUMsU0FBUyxDQUFDO1lBQ3BGLElBQUksQ0FBQ0MsT0FBTyxDQUFDTjtZQUViLHVCQUF1QjtZQUN2Qkgsa0JBQWtCVSxnQkFBZ0JQO1lBRWxDLDJCQUEyQjtZQUMzQixNQUFNUSxTQUFTLE1BQU0sSUFBSSxDQUFDQyxXQUFXLENBQUNULE1BQU1MLGdCQUFnQkU7WUFFNUQsSUFBSVcsT0FBT0UsT0FBTyxFQUFFO2dCQUNsQlosUUFBUUMsR0FBRyxDQUFDLENBQUMsOENBQThDLENBQUM7Z0JBQzVELE9BQU87b0JBQ0xXLFNBQVM7b0JBQ1RDLFNBQVNILE9BQU9HLE9BQU87b0JBQ3ZCWCxNQUFNQTtnQkFDUjtZQUNGLE9BQU87Z0JBQ0xGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGdDQUFnQyxFQUFFUyxPQUFPSSxLQUFLLEVBQUU7Z0JBRTdELGdEQUFnRDtnQkFDaEQsSUFBSSxJQUFJLENBQUNDLGNBQWMsQ0FBQ0wsT0FBT0ksS0FBSyxHQUFHO29CQUNyQ2QsUUFBUUMsR0FBRyxDQUFDLENBQUMsaUdBQWlHLENBQUM7b0JBQy9HLE9BQU87d0JBQ0xXLFNBQVM7d0JBQ1RFLE9BQU8sQ0FBQyw0QkFBNEIsRUFBRUosT0FBT0ksS0FBSyxDQUFDLGtDQUFrQyxDQUFDO3dCQUN0RlosTUFBTUE7d0JBQ05jLGdCQUFnQjtvQkFDbEI7Z0JBQ0Y7Z0JBRUEsT0FBTztvQkFDTEosU0FBUztvQkFDVEUsT0FBT0osT0FBT0ksS0FBSztvQkFDbkJaLE1BQU1BO2dCQUNSO1lBQ0Y7UUFFRixFQUFFLE9BQU9ZLE9BQU87WUFDZCxNQUFNRyxlQUFlSCxpQkFBaUJJLFFBQVFKLE1BQU1LLE9BQU8sR0FBRztZQUM5RG5CLFFBQVFjLEtBQUssQ0FBQyxpQ0FBaUNHO1lBRS9DLGdEQUFnRDtZQUNoRCxJQUFJLElBQUksQ0FBQ0YsY0FBYyxDQUFDRSxlQUFlO2dCQUNyQ2pCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGlHQUFpRyxDQUFDO2dCQUMvRyxPQUFPO29CQUNMVyxTQUFTO29CQUNURSxPQUFPLENBQUMsNEJBQTRCLEVBQUVHLGFBQWEsa0NBQWtDLENBQUM7b0JBQ3RGRCxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxPQUFPO2dCQUNMSixTQUFTO2dCQUNURSxPQUFPLENBQUMsdUJBQXVCLEVBQUVHLGNBQWM7WUFDakQ7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxlQUF1QkEsWUFBb0IsRUFBVztRQUNwRCxNQUFNRyx1QkFBdUI7WUFDM0I7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBRUQsT0FBT0EscUJBQXFCQyxJQUFJLENBQUNDLENBQUFBLFVBQy9CTCxhQUFhTSxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0YsUUFBUUMsV0FBVztJQUUzRDtJQUVBOztHQUVDLEdBQ0QsTUFBY3BCLG1CQUNaUCxLQUFhLEVBQ2JFLFlBQW9CLEVBQ3BCRCxjQUE4QixFQUNQO1FBQ3ZCLE1BQU00QixTQUFTLENBQUMsS0FBSyxFQUFFQyxLQUFLQyxHQUFHLEdBQUcsQ0FBQyxFQUFFQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRyxJQUFJO1FBRTlFLGdDQUFnQztRQUNoQyxNQUFNQyxpQkFBaUIsTUFBTSxJQUFJLENBQUNDLGtCQUFrQixDQUFDckMsT0FBT0UsY0FBY0Q7UUFFMUUsTUFBTUssT0FBcUI7WUFDekJHLElBQUlvQjtZQUNKUyxlQUFldEM7WUFDZnVDLE1BQU1ILGVBQWVHLElBQUksSUFBSSxDQUFDLHNDQUFzQyxFQUFFdkMsT0FBTztZQUM3RVUsVUFBVTBCLGVBQWUxQixRQUFRLElBQUksSUFBSSxDQUFDOEIsa0JBQWtCLENBQUN4QyxPQUFPRTtZQUNwRXVDLFFBQVE7WUFDUkMsVUFBVTtZQUNWQyxjQUFjLENBQUM7WUFDZkMsYUFBYSxFQUFFO1lBQ2ZDLGVBQWUsRUFBRTtZQUNqQkMsbUJBQW1CLEVBQUU7WUFDckJDLGdCQUFnQixFQUFFO1lBQ2xCQyxXQUFXLElBQUlsQixPQUFPbUIsV0FBVztZQUNqQ0MsV0FBVyxJQUFJcEIsT0FBT21CLFdBQVc7UUFDbkM7UUFFQSxPQUFPM0M7SUFDVDtJQUVBOztHQUVDLEdBQ0QsTUFBYytCLG1CQUNackMsS0FBYSxFQUNiRSxZQUFvQixFQUNwQkQsY0FBOEIsRUFDMEI7UUFDeEQsSUFBSTtZQUNGLE1BQU1rRCxRQUFRbEQsZUFBZW1ELGVBQWUsQ0FBQyxFQUFFLEVBQUUseUNBQXlDO1lBQzFGLElBQUksQ0FBQ0QsT0FBTztnQkFDVixNQUFNLElBQUk3QixNQUFNO1lBQ2xCO1lBRUEsTUFBTStCLGlCQUFpQixJQUFJLENBQUNDLG1CQUFtQixDQUFDdEQsT0FBT0U7WUFDdkQsTUFBTXFELFdBQVcsTUFBTSxJQUFJLENBQUNDLGlCQUFpQixDQUFDSCxnQkFBZ0JGO1lBRTlELElBQUlJLFNBQVN2QyxPQUFPLElBQUl1QyxTQUFTdEMsT0FBTyxFQUFFO2dCQUN4QyxPQUFPLElBQUksQ0FBQ3dDLGVBQWUsQ0FBQ0YsU0FBU3RDLE9BQU8sRUFBRWpCO1lBQ2hELE9BQU87Z0JBQ0xJLFFBQVFzRCxJQUFJLENBQUM7Z0JBQ2IsT0FBTztvQkFDTG5CLE1BQU0sQ0FBQyx3QkFBd0IsRUFBRXZDLE9BQU87b0JBQ3hDVSxVQUFVLElBQUksQ0FBQzhCLGtCQUFrQixDQUFDeEMsT0FBT0U7Z0JBQzNDO1lBQ0Y7UUFDRixFQUFFLE9BQU9nQixPQUFPO1lBQ2RkLFFBQVFzRCxJQUFJLENBQUMsdURBQXVEeEM7WUFDcEUsT0FBTztnQkFDTHFCLE1BQU0sQ0FBQyx3QkFBd0IsRUFBRXZDLE9BQU87Z0JBQ3hDVSxVQUFVLElBQUksQ0FBQzhCLGtCQUFrQixDQUFDeEMsT0FBT0U7WUFDM0M7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxvQkFBNEJGLEtBQWEsRUFBRUUsWUFBb0IsRUFBVTtRQUN2RSx3Q0FBd0M7UUFDeEMsTUFBTTZCLE1BQU0sSUFBSUQ7UUFDaEIsTUFBTTZCLGtCQUFrQjVCLElBQUk2QixjQUFjLENBQUMsU0FBUztZQUNsREMsU0FBUztZQUNUQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsS0FBSztZQUNMQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsY0FBYztRQUNoQjtRQUNBLE1BQU1DLGNBQWNyQyxJQUFJc0MsV0FBVztRQUNuQyxNQUFNQyxlQUFldkMsSUFBSTZCLGNBQWMsQ0FBQyxTQUFTO1lBQUVHLE9BQU87UUFBTztRQUVqRSxPQUFPLENBQUMsK0dBQStHLEVBQUUvRCxNQUFNOztxQkFFOUcsRUFBRTJELGdCQUFnQjtjQUN6QixFQUFFUyxZQUFZO2VBQ2IsRUFBRUUsYUFBYTs7ZUFFZixFQUFFcEUsYUFBYTs7OzhCQUdBLEVBQUVrRSxZQUFZOytDQUNHLEVBQUVFLGFBQWEsQ0FBQyxFQUFFRixZQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFnQzNCLEVBQUVFLGFBQWEsQ0FBQyxFQUFFRixZQUFZO3FDQUMzQyxFQUFFQSxZQUFZOzhDQUNMLEVBQUVBLFlBQVk7aUVBQ0ssRUFBRUEsWUFBWTs7b0RBRTNCLENBQUM7SUFDbkQ7SUFFQTs7R0FFQyxHQUNELHlCQUFpQ3BFLEtBQWEsRUFBRXdFLGdCQUEwQixFQUt4RTtRQUNBLE1BQU1KLGNBQWMsSUFBSXRDLE9BQU91QyxXQUFXO1FBQzFDLE1BQU1DLGVBQWUsSUFBSXhDLE9BQU84QixjQUFjLENBQUMsU0FBUztZQUFFRyxPQUFPO1FBQU87UUFFeEUsK0NBQStDO1FBQy9DLE1BQU1VLHFCQUFxQixtREFBbURDLElBQUksQ0FBQzFFO1FBRW5GLE9BQU87WUFDTDJFLFNBQVMzRTtZQUNUNEUsVUFBVUgscUJBQXFCekUsUUFBUSxHQUFHQSxNQUFNLENBQUMsRUFBRW9FLFlBQVksT0FBTyxDQUFDO1lBQ3ZFUyxjQUFjO2dCQUNaN0U7Z0JBQ0EsR0FBR0EsTUFBTSxZQUFZLENBQUM7Z0JBQ3RCLEdBQUdBLE1BQU0sUUFBUSxDQUFDO2dCQUNsQnlFLHFCQUFxQixHQUFHekUsTUFBTSxTQUFTLENBQUMsR0FBRyxHQUFHQSxNQUFNLENBQUMsRUFBRW9FLGFBQWE7YUFDckU7WUFDRFUsYUFBYTtnQkFDWCxHQUFHOUUsTUFBTSxPQUFPLENBQUM7Z0JBQ2pCLEdBQUdBLE1BQU0sWUFBWSxDQUFDO2dCQUN0QixHQUFHQSxNQUFNLENBQUMsRUFBRXNFLGFBQWEsQ0FBQyxFQUFFRixhQUFhO2dCQUN6QyxHQUFHcEUsTUFBTSxlQUFlLENBQUM7YUFDMUI7UUFDSDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxtQkFBMkJBLEtBQWEsRUFBRUUsWUFBb0IsRUFBcUI7UUFDakYsTUFBTTZFLFNBQVNqRCxLQUFLQyxHQUFHO1FBQ3ZCLE1BQU1xQyxjQUFjLElBQUl0QyxPQUFPdUMsV0FBVztRQUMxQyxNQUFNQyxlQUFlLElBQUl4QyxPQUFPOEIsY0FBYyxDQUFDLFNBQVM7WUFBRUcsT0FBTztRQUFPO1FBRXhFLHVDQUF1QztRQUN2QyxNQUFNUyxtQkFBbUI7WUFBQyxHQUFHSixhQUFhO1lBQUUsQ0FBQyxNQUFNLENBQUM7WUFBRSxDQUFDLE1BQU0sQ0FBQztZQUFFLEdBQUdFLGFBQWEsQ0FBQyxFQUFFRixhQUFhO1NBQUM7UUFDakcsTUFBTVksZ0JBQWdCLElBQUksQ0FBQ1Qsd0JBQXdCLENBQUN2RSxPQUFPd0U7UUFFM0QsT0FBTztZQUNMO2dCQUNFL0QsSUFBSSxDQUFDLE9BQU8sRUFBRXNFLE9BQU8sRUFBRSxDQUFDO2dCQUN4QkUsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYmxGLE9BQU9nRixjQUFjTCxPQUFPO2dCQUM1QmxDLFFBQVE7Z0JBQ1IwQyxVQUFVO2dCQUNWQyxVQUFVO2dCQUNWQyxhQUFhO2dCQUNiQyxhQUFhTixjQUFjSCxZQUFZO2dCQUN2Q1UsY0FBYztZQUNoQjtZQUNBO2dCQUNFOUUsSUFBSSxDQUFDLE9BQU8sRUFBRXNFLE9BQU8sRUFBRSxDQUFDO2dCQUN4QkUsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYmxGLE9BQU9nRixjQUFjSixRQUFRO2dCQUM3Qm5DLFFBQVE7Z0JBQ1IwQyxVQUFVO2dCQUNWQyxVQUFVO2dCQUNWQyxhQUFhO2dCQUNiQyxhQUFhTixjQUFjRixXQUFXO2dCQUN0Q1MsY0FBYztZQUNoQjtZQUNBO2dCQUNFOUUsSUFBSSxDQUFDLFFBQVEsRUFBRXNFLFFBQVE7Z0JBQ3ZCRSxNQUFNO2dCQUNOQyxhQUFhO2dCQUNibEYsT0FBTztnQkFDUHlDLFFBQVE7Z0JBQ1IwQyxVQUFVO2dCQUNWQyxVQUFVO2dCQUNWQyxhQUFhO2dCQUNiRyxjQUFjO29CQUFDLENBQUMsT0FBTyxFQUFFVCxPQUFPLEVBQUUsQ0FBQztvQkFBRSxDQUFDLE9BQU8sRUFBRUEsT0FBTyxFQUFFLENBQUM7aUJBQUM7Z0JBQzFEUSxjQUFjO1lBQ2hCO1NBQ0Q7SUFDSDtJQUVBOztHQUVDLEdBQ0QsTUFBY3hFLFlBQ1pULElBQWtCLEVBQ2xCTCxjQUE4QixFQUM5QkUsZ0JBQTJDLEVBQ3NCO1FBQ2pFRyxLQUFLbUMsTUFBTSxHQUFHO1FBQ2RuQyxLQUFLNEMsU0FBUyxHQUFHLElBQUlwQixPQUFPbUIsV0FBVztRQUV2QzdDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDJDQUEyQyxDQUFDO1FBRXpELElBQUk7WUFDRiw4REFBOEQ7WUFDOUQsTUFBTyxJQUFJLENBQUNvRixpQkFBaUIsQ0FBQ25GLE1BQU87Z0JBQ25DLE1BQU1vRixXQUFXLElBQUksQ0FBQ0MscUJBQXFCLENBQUNyRjtnQkFFNUMsSUFBSSxDQUFDb0YsVUFBVTtvQkFDYnRGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLCtFQUErRSxDQUFDO29CQUM3RjtnQkFDRjtnQkFFQUQsUUFBUUMsR0FBRyxDQUFDLENBQUMsdUNBQXVDLEVBQUVxRixTQUFTUixXQUFXLEVBQUU7Z0JBQzVFNUUsS0FBS3NGLGNBQWMsR0FBR0YsU0FBU2pGLEVBQUU7Z0JBQ2pDaUYsU0FBU2pELE1BQU0sR0FBRztnQkFFbEIsc0JBQXNCO2dCQUN0QnRDLGtCQUFrQjBGLGdCQUFnQkgsVUFBVXBGO2dCQUM1Q0gsa0JBQWtCMkYsaUJBQWlCLENBQUMsV0FBVyxFQUFFSixTQUFTUixXQUFXLEVBQUUsRUFBRTVFO2dCQUV6RSxNQUFNeUYsYUFBYSxNQUFNLElBQUksQ0FBQ0MsY0FBYyxDQUFDTixVQUFVcEYsTUFBTUw7Z0JBRTdELElBQUk4RixXQUFXL0UsT0FBTyxFQUFFO29CQUN0QjBFLFNBQVNqRCxNQUFNLEdBQUc7b0JBQ2xCaUQsU0FBUzVFLE1BQU0sR0FBR2lGLFdBQVdFLElBQUk7b0JBQ2pDM0YsS0FBS3dDLGlCQUFpQixDQUFDb0QsSUFBSSxDQUFDUixTQUFTakYsRUFBRTtvQkFDdkNMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHNDQUFzQyxFQUFFcUYsU0FBU1IsV0FBVyxFQUFFO29CQUUzRSx3QkFBd0I7b0JBQ3hCL0Usa0JBQWtCZ0csa0JBQWtCVCxVQUFVcEY7Z0JBQ2hELE9BQU87b0JBQ0xvRixTQUFTTixRQUFRO29CQUNqQk0sU0FBU3hFLEtBQUssR0FBRzZFLFdBQVc3RSxLQUFLO29CQUVqQyxJQUFJd0UsU0FBU04sUUFBUSxJQUFJTSxTQUFTTCxXQUFXLEVBQUU7d0JBQzdDSyxTQUFTakQsTUFBTSxHQUFHO3dCQUNsQm5DLEtBQUt5QyxjQUFjLENBQUNtRCxJQUFJLENBQUNSLFNBQVNqRixFQUFFO3dCQUNwQ0wsUUFBUUMsR0FBRyxDQUFDLENBQUMsK0NBQStDLEVBQUVxRixTQUFTUixXQUFXLEVBQUU7d0JBRXBGLHFCQUFxQjt3QkFDckIvRSxrQkFBa0JpRyxlQUFlVixVQUFVcEY7b0JBQzdDLE9BQU87d0JBQ0xvRixTQUFTakQsTUFBTSxHQUFHO3dCQUNsQnJDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGdEQUFnRCxFQUFFcUYsU0FBU04sUUFBUSxDQUFDLENBQUMsRUFBRU0sU0FBU0wsV0FBVyxDQUFDLEdBQUcsRUFBRUssU0FBU1IsV0FBVyxFQUFFO29CQUN0STtnQkFDRjtnQkFFQSxrQkFBa0I7Z0JBQ2xCNUUsS0FBS29DLFFBQVEsR0FBRyxJQUFJLENBQUMyRCxpQkFBaUIsQ0FBQy9GO2dCQUN2Q0gsa0JBQWtCbUcsbUJBQW1CaEcsS0FBS29DLFFBQVEsRUFBRXBDO2dCQUNwREEsS0FBSzRDLFNBQVMsR0FBRyxJQUFJcEIsT0FBT21CLFdBQVc7Z0JBRXZDLElBQUksQ0FBQ3NELFdBQVcsQ0FBQ2pHO1lBQ25CO1lBRUEsd0JBQXdCO1lBQ3hCLE1BQU1rRyxjQUFjLE1BQU0sSUFBSSxDQUFDQyxxQkFBcUIsQ0FBQ25HLE1BQU1MO1lBQzNESyxLQUFLa0csV0FBVyxHQUFHQTtZQUNuQmxHLEtBQUttQyxNQUFNLEdBQUc7WUFDZG5DLEtBQUtvQyxRQUFRLEdBQUc7WUFFaEIsd0JBQXdCO1lBQ3hCdkMsa0JBQWtCdUcsa0JBQWtCcEc7WUFDcENILGtCQUFrQjJGLGlCQUFpQixvQ0FBb0N4RjtZQUV2RSxPQUFPO2dCQUNMVSxTQUFTO2dCQUNUQyxTQUFTdUY7WUFDWDtRQUVGLEVBQUUsT0FBT3RGLE9BQU87WUFDZFosS0FBS21DLE1BQU0sR0FBRztZQUNkLE1BQU1wQixlQUFlSCxpQkFBaUJJLFFBQVFKLE1BQU1LLE9BQU8sR0FBRztZQUM5RG5CLFFBQVFjLEtBQUssQ0FBQywyQ0FBMkNHO1lBRXpELE9BQU87Z0JBQ0xMLFNBQVM7Z0JBQ1RFLE9BQU9HO1lBQ1Q7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxrQkFBMEJmLElBQWtCLEVBQVc7UUFDckQsT0FBT0EsS0FBS0ksUUFBUSxDQUFDZSxJQUFJLENBQUNrRixDQUFBQSxPQUN4QkEsS0FBS2xFLE1BQU0sS0FBSyxhQUFha0UsS0FBS2xFLE1BQU0sS0FBSztJQUVqRDtJQUVBOztHQUVDLEdBQ0Qsc0JBQThCbkMsSUFBa0IsRUFBMEI7UUFDeEUsTUFBTXNHLGtCQUFrQnRHLEtBQUtJLFFBQVEsQ0FBQ21HLE1BQU0sQ0FBQ0YsQ0FBQUE7WUFDM0MsSUFBSUEsS0FBS2xFLE1BQU0sS0FBSyxXQUFXLE9BQU87WUFFdEMsZ0NBQWdDO1lBQ2hDLElBQUlrRSxLQUFLbkIsWUFBWSxJQUFJbUIsS0FBS25CLFlBQVksQ0FBQzdFLE1BQU0sR0FBRyxHQUFHO2dCQUNyRCxPQUFPZ0csS0FBS25CLFlBQVksQ0FBQ3NCLEtBQUssQ0FBQ0MsQ0FBQUEsUUFDN0J6RyxLQUFLd0MsaUJBQWlCLENBQUNsQixRQUFRLENBQUNtRjtZQUVwQztZQUVBLE9BQU87UUFDVDtRQUVBLG1DQUFtQztRQUNuQ0gsZ0JBQWdCSSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUEsRUFBRS9CLFFBQVEsR0FBRzhCLEVBQUU5QixRQUFRO1FBRXRELE9BQU95QixlQUFlLENBQUMsRUFBRSxJQUFJO0lBQy9CO0lBRUE7O0dBRUMsR0FDRCxNQUFjWixlQUNabUIsT0FBd0IsRUFDeEI3RyxJQUFrQixFQUNsQkwsY0FBOEIsRUFDNkI7UUFDM0QsSUFBSTtZQUNGLE9BQVFrSCxRQUFRbEMsSUFBSTtnQkFDbEIsS0FBSztvQkFDSCxPQUFPLE1BQU0sSUFBSSxDQUFDbUMsb0JBQW9CLENBQUNELFNBQVM3RztnQkFFbEQsS0FBSztvQkFDSCxPQUFPLE1BQU0sSUFBSSxDQUFDK0csc0JBQXNCLENBQUNGLFNBQVM3RztnQkFFcEQsS0FBSztvQkFDSCxPQUFPLE1BQU0sSUFBSSxDQUFDZ0gscUJBQXFCLENBQUNILFNBQVM3RztnQkFFbkQsS0FBSztvQkFDSCxPQUFPLE1BQU0sSUFBSSxDQUFDaUgscUJBQXFCLENBQUNKLFNBQVM3RyxNQUFNTDtnQkFFekQ7b0JBQ0UsTUFBTSxJQUFJcUIsTUFBTSxDQUFDLHNCQUFzQixFQUFFNkYsUUFBUWxDLElBQUksRUFBRTtZQUMzRDtRQUNGLEVBQUUsT0FBTy9ELE9BQU87WUFDZCxNQUFNRyxlQUFlSCxpQkFBaUJJLFFBQVFKLE1BQU1LLE9BQU8sR0FBRztZQUM5RCxPQUFPO2dCQUNMUCxTQUFTO2dCQUNURSxPQUFPRztZQUNUO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBYytGLHFCQUNaRCxPQUF3QixFQUN4QjdHLElBQWtCLEVBQ3lDO1FBQzNELE1BQU1nRixjQUFjO1lBQUM2QixRQUFRbkgsS0FBSztlQUFNbUgsUUFBUTdCLFdBQVcsSUFBSSxFQUFFO1NBQUU7UUFDbkUsSUFBSWtDLFlBQVk7UUFFaEIsdURBQXVEO1FBQ3ZELEtBQUssTUFBTUMsY0FBY25DLFlBQWE7WUFDcEMsSUFBSTtnQkFDRmxGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9DQUFvQyxFQUFFb0gsV0FBVyxDQUFDLENBQUM7Z0JBQ2hFbkgsS0FBS3VDLGFBQWEsQ0FBQ3FELElBQUksQ0FBQ3VCO2dCQUV4QixNQUFNM0csU0FBUyxNQUFNLElBQUksQ0FBQ2xCLGtCQUFrQixDQUFDOEgseUJBQXlCLENBQUNEO2dCQUV2RSxJQUFJM0csT0FBT21GLElBQUksSUFBSW5GLE9BQU9tRixJQUFJLENBQUMwQixPQUFPLElBQUk3RyxPQUFPbUYsSUFBSSxDQUFDMEIsT0FBTyxDQUFDaEgsTUFBTSxHQUFHLEdBQUc7b0JBQ3hFUCxRQUFRQyxHQUFHLENBQUMsQ0FBQyx5QkFBeUIsRUFBRVMsT0FBT21GLElBQUksQ0FBQzBCLE9BQU8sQ0FBQ2hILE1BQU0sQ0FBQyxlQUFlLEVBQUU4RyxXQUFXLENBQUMsQ0FBQztvQkFFakcseUJBQXlCO29CQUN6QjNHLE9BQU9tRixJQUFJLENBQUMwQixPQUFPLENBQUNDLE9BQU8sQ0FBQyxDQUFDQzt3QkFDM0IsSUFBSUEsS0FBS0MsSUFBSSxJQUFJLENBQUN4SCxLQUFLc0MsV0FBVyxDQUFDaEIsUUFBUSxDQUFDaUcsS0FBS0MsSUFBSSxHQUFHOzRCQUN0RHhILEtBQUtzQyxXQUFXLENBQUNzRCxJQUFJLENBQUMyQixLQUFLQyxJQUFJO3dCQUNqQztvQkFDRjtvQkFFQSxPQUFPO3dCQUNMOUcsU0FBUzt3QkFDVGlGLE1BQU1uRixPQUFPbUYsSUFBSTtvQkFDbkI7Z0JBQ0YsT0FBTztvQkFDTHVCLFlBQVksQ0FBQyx1QkFBdUIsRUFBRUMsV0FBVyxDQUFDLENBQUM7b0JBQ25EckgsUUFBUUMsR0FBRyxDQUFDLENBQUMsb0JBQW9CLEVBQUVtSCxXQUFXO2dCQUNoRDtZQUNGLEVBQUUsT0FBT3RHLE9BQU87Z0JBQ2RzRyxZQUFZdEcsaUJBQWlCSSxRQUFRSixNQUFNSyxPQUFPLEdBQUc7Z0JBQ3JEbkIsUUFBUUMsR0FBRyxDQUFDLENBQUMscUNBQXFDLEVBQUVvSCxXQUFXLEdBQUcsRUFBRUQsV0FBVztZQUNqRjtRQUNGO1FBRUEsT0FBTztZQUNMeEcsU0FBUztZQUNURSxPQUFPLENBQUMscUNBQXFDLEVBQUVzRyxXQUFXO1FBQzVEO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWNILHVCQUNaRixPQUF3QixFQUN4QjdHLElBQWtCLEVBQ3lDO1FBQzNELElBQUk7WUFDRixNQUFNTixRQUFRbUgsUUFBUW5ILEtBQUs7WUFDM0JJLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGdEQUFnRCxFQUFFTCxPQUFPO1lBRXRFLG9DQUFvQztZQUNwQyxNQUFNK0gsYUFBYTtZQUNuQixJQUFJQSxXQUFXckQsSUFBSSxDQUFDMUUsUUFBUTtnQkFDMUIsd0JBQXdCO2dCQUN4QkksUUFBUUMsR0FBRyxDQUFDLENBQUMsdUNBQXVDLEVBQUVMLE9BQU87Z0JBRTdELElBQUksQ0FBQ00sS0FBS3NDLFdBQVcsQ0FBQ2hCLFFBQVEsQ0FBQzVCLFFBQVE7b0JBQ3JDTSxLQUFLc0MsV0FBVyxDQUFDc0QsSUFBSSxDQUFDbEc7Z0JBQ3hCO2dCQUVBLE1BQU1jLFNBQVMsTUFBTSxJQUFJLENBQUNsQixrQkFBa0IsQ0FBQ29JLGtCQUFrQixDQUFDaEk7Z0JBRWhFLElBQUljLE9BQU9tRixJQUFJLEVBQUU7b0JBQ2YsT0FBTzt3QkFDTGpGLFNBQVM7d0JBQ1RpRixNQUFNbkYsT0FBT21GLElBQUk7b0JBQ25CO2dCQUNGLE9BQU87b0JBQ0wsT0FBTzt3QkFDTGpGLFNBQVM7d0JBQ1RFLE9BQU87b0JBQ1Q7Z0JBQ0Y7WUFDRixPQUFPO2dCQUNMLDRDQUE0QztnQkFDNUNkLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHFEQUFxRCxFQUFFTCxPQUFPO2dCQUUzRSxrRUFBa0U7Z0JBQ2xFLElBQUlpSSxjQUFjakk7Z0JBQ2xCLElBQUlBLE1BQU0yQixXQUFXLEdBQUdDLFFBQVEsQ0FBQyxnQkFBZ0I7b0JBQy9DcUcsY0FBY2pJLE1BQU1rSSxPQUFPLENBQUMsbUJBQW1CLElBQUlDLElBQUk7Z0JBQ3pEO2dCQUNBLElBQUluSSxNQUFNMkIsV0FBVyxHQUFHQyxRQUFRLENBQUMsZ0JBQWdCO29CQUMvQ3FHLGNBQWNBLFlBQVlDLE9BQU8sQ0FBQyxtQkFBbUIsSUFBSUMsSUFBSTtnQkFDL0Q7Z0JBRUEsdUNBQXVDO2dCQUN2QyxPQUFPLE1BQU0sSUFBSSxDQUFDZixvQkFBb0IsQ0FBQztvQkFDckMsR0FBR0QsT0FBTztvQkFDVmxDLE1BQU07b0JBQ05qRixPQUFPaUk7Z0JBQ1QsR0FBRzNIO1lBQ0w7UUFDRixFQUFFLE9BQU9ZLE9BQU87WUFDZCxPQUFPO2dCQUNMRixTQUFTO2dCQUNURSxPQUFPQSxpQkFBaUJJLFFBQVFKLE1BQU1LLE9BQU8sR0FBRztZQUNsRDtRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWMrRixzQkFDWkgsT0FBd0IsRUFDeEI3RyxJQUFrQixFQUN5QztRQUMzRCx5REFBeUQ7UUFDekQsT0FBTyxJQUFJLENBQUMrRyxzQkFBc0IsQ0FBQ0YsU0FBUzdHO0lBQzlDO0lBRUE7O0dBRUMsR0FDRCxNQUFjaUgsc0JBQ1pKLE9BQXdCLEVBQ3hCN0csSUFBa0IsRUFDbEJMLGNBQThCLEVBQzZCO1FBQzNELElBQUk7WUFDRkcsUUFBUUMsR0FBRyxDQUFDLENBQUMsOENBQThDLENBQUM7WUFFNUQsMkNBQTJDO1lBQzNDLE1BQU1zQyxlQUFlckMsS0FBS0ksUUFBUSxDQUMvQm1HLE1BQU0sQ0FBQ0YsQ0FBQUEsT0FBUUEsS0FBS2xFLE1BQU0sS0FBSyxlQUFla0UsS0FBSzdGLE1BQU0sRUFDekRzSCxHQUFHLENBQUN6QixDQUFBQSxPQUFTO29CQUNaMUIsTUFBTTBCLEtBQUsxQixJQUFJO29CQUNmQyxhQUFheUIsS0FBS3pCLFdBQVc7b0JBQzdCbEYsT0FBTzJHLEtBQUszRyxLQUFLO29CQUNqQmlHLE1BQU1VLEtBQUs3RixNQUFNO2dCQUNuQjtZQUVGLElBQUk2QixhQUFhaEMsTUFBTSxLQUFLLEdBQUc7Z0JBQzdCLE9BQU87b0JBQ0xLLFNBQVM7b0JBQ1RFLE9BQU87Z0JBQ1Q7WUFDRjtZQUVBLDRDQUE0QztZQUM1QyxNQUFNaUMsUUFBUWxELGVBQWVtRCxlQUFlLENBQUMsRUFBRTtZQUMvQyxJQUFJLENBQUNELE9BQU87Z0JBQ1YsT0FBTztvQkFDTG5DLFNBQVM7b0JBQ1RFLE9BQU87Z0JBQ1Q7WUFDRjtZQUVBLE1BQU1tSCxpQkFBaUIsSUFBSSxDQUFDQyxtQkFBbUIsQ0FBQ2hJLEtBQUtnQyxhQUFhLEVBQUVLO1lBQ3BFLE1BQU1ZLFdBQVcsTUFBTSxJQUFJLENBQUNnRixpQkFBaUIsQ0FBQ0YsZ0JBQWdCbEY7WUFFOUQsSUFBSUksU0FBU3ZDLE9BQU8sSUFBSXVDLFNBQVN0QyxPQUFPLEVBQUU7Z0JBQ3hDLE9BQU87b0JBQ0xELFNBQVM7b0JBQ1RpRixNQUFNO3dCQUNKdUMsVUFBVWpGLFNBQVN0QyxPQUFPO3dCQUMxQndILFlBQVk5RjtvQkFDZDtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wsT0FBTztvQkFDTDNCLFNBQVM7b0JBQ1RFLE9BQU9xQyxTQUFTckMsS0FBSyxJQUFJO2dCQUMzQjtZQUNGO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2QsT0FBTztnQkFDTEYsU0FBUztnQkFDVEUsT0FBT0EsaUJBQWlCSSxRQUFRSixNQUFNSyxPQUFPLEdBQUc7WUFDbEQ7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxrQkFBMEJqQixJQUFrQixFQUFVO1FBQ3BELE1BQU1vSSxhQUFhcEksS0FBS0ksUUFBUSxDQUFDQyxNQUFNO1FBQ3ZDLE1BQU1nSSxpQkFBaUJySSxLQUFLd0MsaUJBQWlCLENBQUNuQyxNQUFNO1FBQ3BELE9BQU9xQixLQUFLNEcsS0FBSyxDQUFDLGlCQUFrQkYsYUFBYztJQUNwRDtJQUVBOztHQUVDLEdBQ0QsWUFBb0JwSSxJQUFrQixFQUFRO1FBQzVDLE1BQU11SSxZQUFZdkksS0FBS3dDLGlCQUFpQixDQUFDbkMsTUFBTTtRQUMvQyxNQUFNbUksU0FBU3hJLEtBQUt5QyxjQUFjLENBQUNwQyxNQUFNO1FBQ3pDLE1BQU1vSSxRQUFRekksS0FBS0ksUUFBUSxDQUFDQyxNQUFNO1FBQ2xDLE1BQU1xSSxZQUFZRCxRQUFRRixZQUFZQztRQUV0QzFJLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDhCQUE4QixFQUFFQyxLQUFLb0MsUUFBUSxDQUFDLEdBQUcsRUFBRW1HLFVBQVUsQ0FBQyxFQUFFRSxNQUFNLFlBQVksRUFBRUQsT0FBTyxTQUFTLEVBQUVFLFVBQVUsV0FBVyxDQUFDO0lBQzNJO0lBRUE7O0dBRUMsR0FDRCxvQkFBNEIxRyxhQUFxQixFQUFFSyxZQUFtQixFQUFVO1FBQzlFLHdDQUF3QztRQUN4QyxNQUFNWixNQUFNLElBQUlEO1FBQ2hCLE1BQU02QixrQkFBa0I1QixJQUFJNkIsY0FBYyxDQUFDLFNBQVM7WUFDbERDLFNBQVM7WUFDVEMsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLEtBQUs7WUFDTEMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLGNBQWM7UUFDaEI7UUFFQSxNQUFNOEUsY0FBY3RHLGFBQWF5RixHQUFHLENBQUMsQ0FBQ1AsTUFBTXFCLFFBQzFDLENBQUMsT0FBTyxFQUFFQSxRQUFRLEVBQUUsRUFBRSxFQUFFckIsS0FBSzVDLElBQUksQ0FBQyxHQUFHLEVBQUU0QyxLQUFLM0MsV0FBVyxDQUFDLFNBQVMsRUFBRTJDLEtBQUs3SCxLQUFLLENBQUMsUUFBUSxFQUFFbUosS0FBS0MsU0FBUyxDQUFDdkIsS0FBSzVCLElBQUksRUFBRSxNQUFNLElBQUksRUFDNUhvRCxJQUFJLENBQUM7UUFFUCxPQUFPLENBQUM7O3FCQUVTLEVBQUUxRixnQkFBZ0I7aUJBQ3RCLEVBQUVyQixjQUFjOzs7QUFHakMsRUFBRTJHLFlBQVk7Ozs7Ozs7Ozs7OzsySEFZNkcsQ0FBQztJQUMxSDtJQUVBOztHQUVDLEdBQ0QsTUFBY3pGLGtCQUNaOEYsTUFBYyxFQUNkbkcsS0FBb0IsRUFDNkM7UUFDakUsSUFBSTtZQUNGLE1BQU1vRyxtQkFBbUIsSUFBSSxDQUFDQyxtQkFBbUIsQ0FBQ3JHO1lBRWxELE1BQU1zRyxXQUFXO2dCQUNmO29CQUNFQyxNQUFNO29CQUNOekksU0FBU3FJO2dCQUNYO2FBQ0Q7WUFFRCxJQUFJSztZQUNKLElBQUlDO1lBQ0osSUFBSUM7WUFFSiwwRUFBMEU7WUFDMUUsT0FBUTFHLE1BQU0yRyxRQUFRO2dCQUNwQixLQUFLO29CQUNISCxTQUFTO29CQUNUQyxVQUFVO3dCQUNSLGdCQUFnQjt3QkFDaEIsaUJBQWlCLENBQUMsT0FBTyxFQUFFekcsTUFBTTRHLE9BQU8sRUFBRTtvQkFDNUM7b0JBQ0FGLE9BQU87d0JBQ0wxRyxPQUFPb0c7d0JBQ1BFO3dCQUNBTyxhQUFhO3dCQUNiQyxZQUFZO29CQUNkO29CQUNBO2dCQUVGLEtBQUs7b0JBQ0hOLFNBQVM7b0JBQ1RDLFVBQVU7d0JBQ1IsZ0JBQWdCO3dCQUNoQixpQkFBaUIsQ0FBQyxPQUFPLEVBQUV6RyxNQUFNNEcsT0FBTyxFQUFFO29CQUM1QztvQkFDQUYsT0FBTzt3QkFDTDFHLE9BQU9vRzt3QkFDUEU7d0JBQ0FPLGFBQWE7d0JBQ2JDLFlBQVk7b0JBQ2Q7b0JBQ0E7Z0JBRUYsS0FBSztvQkFDSE4sU0FBUztvQkFDVEMsVUFBVTt3QkFDUixnQkFBZ0I7d0JBQ2hCLGFBQWF6RyxNQUFNNEcsT0FBTzt3QkFDMUIscUJBQXFCO29CQUN2QjtvQkFDQUYsT0FBTzt3QkFDTDFHLE9BQU9vRzt3QkFDUEU7d0JBQ0FPLGFBQWE7d0JBQ2JDLFlBQVk7b0JBQ2Q7b0JBQ0E7Z0JBRUYsS0FBSztvQkFDSE4sU0FBUztvQkFDVEMsVUFBVTt3QkFDUixnQkFBZ0I7d0JBQ2hCLGlCQUFpQixDQUFDLE9BQU8sRUFBRXpHLE1BQU00RyxPQUFPLEVBQUU7d0JBQzFDLGdCQUFnQjt3QkFDaEIsV0FBVztvQkFDYjtvQkFDQUYsT0FBTzt3QkFDTDFHLE9BQU9vRzt3QkFDUEU7d0JBQ0FPLGFBQWE7d0JBQ2JDLFlBQVk7b0JBQ2Q7b0JBQ0E7Z0JBRUY7b0JBQ0UsTUFBTSxJQUFJM0ksTUFBTSxDQUFDLG1DQUFtQyxFQUFFNkIsTUFBTTJHLFFBQVEsRUFBRTtZQUMxRTtZQUVBLE1BQU1JLFdBQVcsTUFBTUMsTUFBTVIsUUFBUTtnQkFDbkNTLFFBQVE7Z0JBQ1JSO2dCQUNBQyxNQUFNVixLQUFLQyxTQUFTLENBQUNTO2dCQUNyQlEsUUFBUUMsWUFBWUMsT0FBTyxDQUFDO1lBQzlCO1lBRUEsSUFBSSxDQUFDTCxTQUFTTSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU1DLFlBQVksTUFBTVAsU0FBU1EsSUFBSTtnQkFDckMsTUFBTSxJQUFJcEosTUFBTSxDQUFDLFdBQVcsRUFBRTRJLFNBQVN6SCxNQUFNLENBQUMsR0FBRyxFQUFFZ0ksV0FBVztZQUNoRTtZQUVBLE1BQU0zSixTQUFTLE1BQU1vSixTQUFTUyxJQUFJO1lBRWxDLElBQUkxSjtZQUNKLElBQUlrQyxNQUFNMkcsUUFBUSxLQUFLLGFBQWE7Z0JBQ2xDN0ksVUFBVUgsT0FBT0csT0FBTyxFQUFFLENBQUMsRUFBRSxFQUFFeUo7WUFDakMsT0FBTztnQkFDTHpKLFVBQVVILE9BQU84SixPQUFPLEVBQUUsQ0FBQyxFQUFFLEVBQUVySixTQUFTTjtZQUMxQztZQUVBLElBQUksQ0FBQ0EsU0FBUztnQkFDWixNQUFNLElBQUlLLE1BQU07WUFDbEI7WUFFQSxPQUFPO2dCQUFFTixTQUFTO2dCQUFNQztZQUFRO1FBRWxDLEVBQUUsT0FBT0MsT0FBTztZQUNkLE1BQU1HLGVBQWVILGlCQUFpQkksUUFBUUosTUFBTUssT0FBTyxHQUFHO1lBQzlELE9BQU87Z0JBQUVQLFNBQVM7Z0JBQU9FLE9BQU9HO1lBQWE7UUFDL0M7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBY2tILGtCQUNaZSxNQUFjLEVBQ2RuRyxLQUFvQixFQUM2QztRQUNqRSxPQUFPLElBQUksQ0FBQ0ssaUJBQWlCLENBQUM4RixRQUFRbkcsUUFBUSxzQkFBc0I7SUFDdEU7SUFFQTs7R0FFQyxHQUNELG9CQUE0QkEsS0FBb0IsRUFBVTtRQUN4RCxJQUFJQSxNQUFNMkcsUUFBUSxDQUFDbkksV0FBVyxPQUFPLGNBQWM7WUFDakQsT0FBT3dCLE1BQU1BLEtBQUs7UUFDcEI7UUFDQSxNQUFNMEgsUUFBUTFILE1BQU1BLEtBQUssQ0FBQzJILEtBQUssQ0FBQztRQUNoQyxPQUFPRCxNQUFNbEssTUFBTSxHQUFHLElBQUlrSyxLQUFLLENBQUNBLE1BQU1sSyxNQUFNLEdBQUcsRUFBRSxHQUFHd0MsTUFBTUEsS0FBSztJQUNqRTtJQUVBOztHQUVDLEdBQ0QsZ0JBQXdCNEgsVUFBa0IsRUFBRXpJLGFBQXFCLEVBQWlEO1FBQ2hILElBQUk7WUFDRix3Q0FBd0M7WUFDeEMsTUFBTTBJLFlBQVlELFdBQVdFLEtBQUssQ0FBQztZQUNuQyxJQUFJLENBQUNELFdBQVc7Z0JBQ2QsTUFBTSxJQUFJMUosTUFBTTtZQUNsQjtZQUVBLE1BQU00SixTQUFTL0IsS0FBS2dDLEtBQUssQ0FBQ0gsU0FBUyxDQUFDLEVBQUU7WUFFdEMsSUFBSSxDQUFDRSxPQUFPM0ksSUFBSSxJQUFJLENBQUMySSxPQUFPeEssUUFBUSxJQUFJLENBQUMwSyxNQUFNQyxPQUFPLENBQUNILE9BQU94SyxRQUFRLEdBQUc7Z0JBQ3ZFLE1BQU0sSUFBSVksTUFBTTtZQUNsQjtZQUVBLG9DQUFvQztZQUNwQyxNQUFNWixXQUE4QndLLE9BQU94SyxRQUFRLENBQUMwSCxHQUFHLENBQUMsQ0FBQ3pCLE1BQVd1QyxRQUFtQjtvQkFDckZ6SSxJQUFJa0csS0FBS2xHLEVBQUUsSUFBSSxDQUFDLFFBQVEsRUFBRXFCLEtBQUtDLEdBQUcsR0FBRyxDQUFDLEVBQUVtSCxPQUFPO29CQUMvQ2pFLE1BQU0wQixLQUFLMUIsSUFBSSxJQUFJO29CQUNuQkMsYUFBYXlCLEtBQUt6QixXQUFXLElBQUksQ0FBQyxLQUFLLEVBQUVnRSxRQUFRLEdBQUc7b0JBQ3BEbEosT0FBTzJHLEtBQUszRyxLQUFLLElBQUlzQztvQkFDckJHLFFBQVE7b0JBQ1IwQyxVQUFVd0IsS0FBS3hCLFFBQVEsSUFBSTtvQkFDM0JDLFVBQVU7b0JBQ1ZDLGFBQWE7b0JBQ2JDLGFBQWFxQixLQUFLckIsV0FBVyxJQUFJLEVBQUU7b0JBQ25DQyxjQUFjb0IsS0FBS3BCLFlBQVksSUFBSTtnQkFDckM7WUFFQSxPQUFPO2dCQUNMaEQsTUFBTTJJLE9BQU8zSSxJQUFJO2dCQUNqQjdCO1lBQ0Y7UUFFRixFQUFFLE9BQU9RLE9BQU87WUFDZGQsUUFBUXNELElBQUksQ0FBQyw2REFBNkR4QztZQUMxRSxPQUFPO2dCQUNMcUIsTUFBTSxDQUFDLHdCQUF3QixFQUFFRCxlQUFlO2dCQUNoRDVCLFVBQVUsSUFBSSxDQUFDOEIsa0JBQWtCLENBQUNGLGVBQWU7WUFDbkQ7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFjbUUsc0JBQ1puRyxJQUFrQixFQUNsQkwsY0FBOEIsRUFDYjtRQUNqQixJQUFJO1lBQ0Ysd0NBQXdDO1lBQ3hDLE1BQU1xTCxlQUFlaEwsS0FBS0ksUUFBUSxDQUFDNkssSUFBSSxDQUFDNUUsQ0FBQUEsT0FDdENBLEtBQUsxQixJQUFJLEtBQUssYUFBYTBCLEtBQUtsRSxNQUFNLEtBQUssZUFBZWtFLEtBQUs3RixNQUFNO1lBR3ZFLElBQUl3SyxnQkFBZ0JBLGFBQWF4SyxNQUFNLEVBQUUwSCxVQUFVO2dCQUNqRCxPQUFPOEMsYUFBYXhLLE1BQU0sQ0FBQzBILFFBQVE7WUFDckM7WUFFQSxpRUFBaUU7WUFDakUsTUFBTUcsaUJBQWlCckksS0FBS0ksUUFBUSxDQUFDbUcsTUFBTSxDQUFDRixDQUFBQSxPQUMxQ0EsS0FBS2xFLE1BQU0sS0FBSyxlQUFla0UsS0FBSzdGLE1BQU07WUFHNUMsSUFBSTZILGVBQWVoSSxNQUFNLEtBQUssR0FBRztnQkFDL0IsT0FBTyxDQUFDLHlEQUF5RCxFQUFFTCxLQUFLZ0MsYUFBYSxDQUFDLENBQUMsQ0FBQztZQUMxRjtZQUVBLHlCQUF5QjtZQUN6QixJQUFJa0osVUFBVSxDQUFDLGdDQUFnQyxFQUFFbEwsS0FBS2dDLGFBQWEsQ0FBQyxNQUFNLENBQUM7WUFFM0VxRyxlQUFlZixPQUFPLENBQUMsQ0FBQ2pCLE1BQU11QztnQkFDNUJzQyxXQUFXLEdBQUd0QyxRQUFRLEVBQUUsRUFBRSxFQUFFdkMsS0FBS3pCLFdBQVcsQ0FBQyxHQUFHLENBQUM7Z0JBQ2pELElBQUl5QixLQUFLN0YsTUFBTSxFQUFFNkcsV0FBV3lELE1BQU1DLE9BQU8sQ0FBQzFFLEtBQUs3RixNQUFNLENBQUM2RyxPQUFPLEdBQUc7b0JBQzlEaEIsS0FBSzdGLE1BQU0sQ0FBQzZHLE9BQU8sQ0FBQzhELEtBQUssQ0FBQyxHQUFHLEdBQUc3RCxPQUFPLENBQUMsQ0FBQzlHO3dCQUN2QzBLLFdBQVcsQ0FBQyxLQUFLLEVBQUUxSyxPQUFPNEssS0FBSyxJQUFJLFNBQVMsRUFBRSxDQUFDO29CQUNqRDtnQkFDRixPQUFPLElBQUksT0FBTy9FLEtBQUs3RixNQUFNLEtBQUssVUFBVTtvQkFDMUMwSyxXQUFXLENBQUMsR0FBRyxFQUFFN0UsS0FBSzdGLE1BQU0sQ0FBQzZLLFNBQVMsQ0FBQyxHQUFHLEtBQUssS0FBSyxDQUFDO2dCQUN2RDtnQkFDQUgsV0FBVztZQUNiO1lBRUEsT0FBT0E7UUFFVCxFQUFFLE9BQU90SyxPQUFPO1lBQ2RkLFFBQVFjLEtBQUssQ0FBQyxxREFBcURBO1lBQ25FLE9BQU8sQ0FBQyx3QkFBd0IsRUFBRVosS0FBS2dDLGFBQWEsQ0FBQywyRUFBMkUsQ0FBQztRQUNuSTtJQUNGO0lBRUE7O0dBRUMsR0FDRCxRQUFnQmhDLElBQWtCLEVBQVE7UUFDeENGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGtDQUFrQyxDQUFDO1FBQ2hERCxRQUFRQyxHQUFHLENBQUMsQ0FBQyx1QkFBdUIsRUFBRUMsS0FBS2lDLElBQUksRUFBRTtRQUNqRG5DLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDBCQUEwQixDQUFDO1FBQ3hDQyxLQUFLSSxRQUFRLENBQUNrSCxPQUFPLENBQUMsQ0FBQ1QsU0FBUytCO1lBQzlCOUksUUFBUUMsR0FBRyxDQUFDLENBQUMsbUJBQW1CLEVBQUU2SSxRQUFRLEVBQUUsR0FBRyxFQUFFL0IsUUFBUWxDLElBQUksQ0FBQzJHLFdBQVcsR0FBRyxFQUFFLEVBQUV6RSxRQUFRakMsV0FBVyxFQUFFO1lBQ3JHOUUsUUFBUUMsR0FBRyxDQUFDLENBQUMsOEJBQThCLEVBQUU4RyxRQUFRbkgsS0FBSyxDQUFDLENBQUMsQ0FBQztZQUM3REksUUFBUUMsR0FBRyxDQUFDLENBQUMsZ0NBQWdDLEVBQUU4RyxRQUFRaEMsUUFBUSxDQUFDLFVBQVUsRUFBRWdDLFFBQVExRSxNQUFNLEVBQUU7WUFDNUYsSUFBSTBFLFFBQVE3QixXQUFXLElBQUk2QixRQUFRN0IsV0FBVyxDQUFDM0UsTUFBTSxHQUFHLEdBQUc7Z0JBQ3pEUCxRQUFRQyxHQUFHLENBQUMsQ0FBQyxpQ0FBaUMsRUFBRThHLFFBQVE3QixXQUFXLENBQUMrRCxJQUFJLENBQUMsT0FBTztZQUNsRjtRQUNGO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGxpYlxcYnJvd3NpbmdcXFNtYXJ0QnJvd3NpbmdFeGVjdXRvci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTbWFydCBCcm93c2luZyBFeGVjdXRvciAtIEludGVsbGlnZW50IHBsYW4tYmFzZWQgYnJvd3Npbmcgd2l0aCB0b2RvIGxpc3QgbWFuYWdlbWVudFxuLy8gSGFuZGxlcyBjb21wbGV4IGJyb3dzaW5nIHRhc2tzIGJ5IGNyZWF0aW5nIHBsYW5zLCBleGVjdXRpbmcgc3VidGFza3MsIGFuZCB1cGRhdGluZyBwcm9ncmVzc1xuXG5pbXBvcnQgQnJvd3Nlcmxlc3NTZXJ2aWNlIGZyb20gJ0AvbGliL2Jyb3dzZXJsZXNzJztcbmltcG9ydCB7IEJyb3dzaW5nTW9kZWwgfSBmcm9tICdAL3R5cGVzL2N1c3RvbUFwaUNvbmZpZ3MnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEJyb3dzaW5nU3VidGFzayB7XG4gIGlkOiBzdHJpbmc7XG4gIHR5cGU6ICdzZWFyY2gnIHwgJ25hdmlnYXRlJyB8ICdleHRyYWN0JyB8ICdhbmFseXplJztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgcXVlcnk6IHN0cmluZztcbiAgc3RhdHVzOiAncGVuZGluZycgfCAnaW5fcHJvZ3Jlc3MnIHwgJ2NvbXBsZXRlZCcgfCAnZmFpbGVkJztcbiAgcHJpb3JpdHk6IG51bWJlcjsgLy8gMS0xMCwgaGlnaGVyID0gbW9yZSBpbXBvcnRhbnRcbiAgZGVwZW5kZW5jaWVzPzogc3RyaW5nW107IC8vIElEcyBvZiBzdWJ0YXNrcyB0aGF0IG11c3QgY29tcGxldGUgZmlyc3RcbiAgcmVzdWx0PzogYW55O1xuICBlcnJvcj86IHN0cmluZztcbiAgYXR0ZW1wdHM6IG51bWJlcjtcbiAgbWF4QXR0ZW1wdHM6IG51bWJlcjtcbiAgc2VhcmNoVGVybXM/OiBzdHJpbmdbXTsgLy8gQWx0ZXJuYXRpdmUgc2VhcmNoIHRlcm1zIHRvIHRyeVxuICBleHBlY3RlZEluZm8/OiBzdHJpbmc7IC8vIFdoYXQgaW5mb3JtYXRpb24gd2UgZXhwZWN0IHRvIGZpbmRcbn1cblxuZXhwb3J0IGludGVyZmFjZSBCcm93c2luZ1BsYW4ge1xuICBpZDogc3RyaW5nO1xuICBvcmlnaW5hbFF1ZXJ5OiBzdHJpbmc7XG4gIGdvYWw6IHN0cmluZztcbiAgc3VidGFza3M6IEJyb3dzaW5nU3VidGFza1tdO1xuICBzdGF0dXM6ICdwbGFubmluZycgfCAnZXhlY3V0aW5nJyB8ICdjb21wbGV0ZWQnIHwgJ2ZhaWxlZCc7XG4gIHByb2dyZXNzOiBudW1iZXI7IC8vIDAtMTAwXG4gIGdhdGhlcmVkRGF0YTogUmVjb3JkPHN0cmluZywgYW55PjtcbiAgdmlzaXRlZFVybHM6IHN0cmluZ1tdO1xuICBzZWFyY2hRdWVyaWVzOiBzdHJpbmdbXTtcbiAgY29tcGxldGVkU3VidGFza3M6IHN0cmluZ1tdO1xuICBmYWlsZWRTdWJ0YXNrczogc3RyaW5nW107XG4gIGN1cnJlbnRTdWJ0YXNrPzogc3RyaW5nO1xuICBmaW5hbFJlc3VsdD86IHN0cmluZztcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XG4gIHVwZGF0ZWRBdDogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgQnJvd3NpbmdDb25maWcge1xuICBicm93c2luZ19lbmFibGVkOiBib29sZWFuO1xuICBicm93c2luZ19tb2RlbHM6IEJyb3dzaW5nTW9kZWxbXTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBCcm93c2luZ1Byb2dyZXNzQ2FsbGJhY2sge1xuICBvblBsYW5DcmVhdGVkPzogKHBsYW46IEJyb3dzaW5nUGxhbikgPT4gdm9pZDtcbiAgb25UYXNrU3RhcnRlZD86ICh0YXNrOiBCcm93c2luZ1N1YnRhc2ssIHBsYW46IEJyb3dzaW5nUGxhbikgPT4gdm9pZDtcbiAgb25UYXNrQ29tcGxldGVkPzogKHRhc2s6IEJyb3dzaW5nU3VidGFzaywgcGxhbjogQnJvd3NpbmdQbGFuKSA9PiB2b2lkO1xuICBvblRhc2tGYWlsZWQ/OiAodGFzazogQnJvd3NpbmdTdWJ0YXNrLCBwbGFuOiBCcm93c2luZ1BsYW4pID0+IHZvaWQ7XG4gIG9uUHJvZ3Jlc3NVcGRhdGU/OiAocHJvZ3Jlc3M6IG51bWJlciwgcGxhbjogQnJvd3NpbmdQbGFuKSA9PiB2b2lkO1xuICBvblN0YXR1c1VwZGF0ZT86IChzdGF0dXM6IHN0cmluZywgcGxhbjogQnJvd3NpbmdQbGFuKSA9PiB2b2lkO1xuICBvblBsYW5Db21wbGV0ZWQ/OiAocGxhbjogQnJvd3NpbmdQbGFuKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgY2xhc3MgU21hcnRCcm93c2luZ0V4ZWN1dG9yIHtcbiAgcHJpdmF0ZSBzdGF0aWMgaW5zdGFuY2U6IFNtYXJ0QnJvd3NpbmdFeGVjdXRvcjtcbiAgcHJpdmF0ZSBicm93c2VybGVzc1NlcnZpY2U6IEJyb3dzZXJsZXNzU2VydmljZTtcbiAgcHJpdmF0ZSBhY3RpdmVQbGFuczogTWFwPHN0cmluZywgQnJvd3NpbmdQbGFuPiA9IG5ldyBNYXAoKTtcblxuICBjb25zdHJ1Y3RvcigpIHtcbiAgICB0aGlzLmJyb3dzZXJsZXNzU2VydmljZSA9IEJyb3dzZXJsZXNzU2VydmljZS5nZXRJbnN0YW5jZSgpO1xuICB9XG5cbiAgc3RhdGljIGdldEluc3RhbmNlKCk6IFNtYXJ0QnJvd3NpbmdFeGVjdXRvciB7XG4gICAgaWYgKCFTbWFydEJyb3dzaW5nRXhlY3V0b3IuaW5zdGFuY2UpIHtcbiAgICAgIFNtYXJ0QnJvd3NpbmdFeGVjdXRvci5pbnN0YW5jZSA9IG5ldyBTbWFydEJyb3dzaW5nRXhlY3V0b3IoKTtcbiAgICB9XG4gICAgcmV0dXJuIFNtYXJ0QnJvd3NpbmdFeGVjdXRvci5pbnN0YW5jZTtcbiAgfVxuXG4gIC8qKlxuICAgKiBFeGVjdXRlIHNtYXJ0IGJyb3dzaW5nIHdpdGggcGxhbm5pbmcgYW5kIHRvZG8gbGlzdCBtYW5hZ2VtZW50XG4gICAqL1xuICBhc3luYyBleGVjdXRlU21hcnRCcm93c2luZyhcbiAgICBxdWVyeTogc3RyaW5nLFxuICAgIGJyb3dzaW5nQ29uZmlnOiBCcm93c2luZ0NvbmZpZyxcbiAgICBicm93c2luZ1R5cGU6ICdzZWFyY2gnIHwgJ25hdmlnYXRlJyB8ICdleHRyYWN0JyA9ICdzZWFyY2gnLFxuICAgIHByb2dyZXNzQ2FsbGJhY2s/OiBCcm93c2luZ1Byb2dyZXNzQ2FsbGJhY2tcbiAgKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGNvbnRlbnQ/OiBzdHJpbmc7IGVycm9yPzogc3RyaW5nOyBwbGFuPzogQnJvd3NpbmdQbGFuIH0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coYFtTbWFydCBCcm93c2luZ10g8J+OryBTdGFydGluZyBpbnRlbGxpZ2VudCBicm93c2luZyBmb3I6IFwiJHtxdWVyeX1cImApO1xuXG4gICAgICAvLyBTdGVwIDE6IENyZWF0ZSBhIGJyb3dzaW5nIHBsYW5cbiAgICAgIGNvbnN0IHBsYW4gPSBhd2FpdCB0aGlzLmNyZWF0ZUJyb3dzaW5nUGxhbihxdWVyeSwgYnJvd3NpbmdUeXBlLCBicm93c2luZ0NvbmZpZyk7XG4gICAgICB0aGlzLmFjdGl2ZVBsYW5zLnNldChwbGFuLmlkLCBwbGFuKTtcblxuICAgICAgY29uc29sZS5sb2coYFtTbWFydCBCcm93c2luZ10g8J+TiyBDcmVhdGVkIHBsYW4gd2l0aCAke3BsYW4uc3VidGFza3MubGVuZ3RofSBzdWJ0YXNrc2ApO1xuICAgICAgdGhpcy5sb2dQbGFuKHBsYW4pO1xuXG4gICAgICAvLyBOb3RpZnkgcGxhbiBjcmVhdGlvblxuICAgICAgcHJvZ3Jlc3NDYWxsYmFjaz8ub25QbGFuQ3JlYXRlZD8uKHBsYW4pO1xuXG4gICAgICAvLyBTdGVwIDI6IEV4ZWN1dGUgdGhlIHBsYW5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRoaXMuZXhlY3V0ZVBsYW4ocGxhbiwgYnJvd3NpbmdDb25maWcsIHByb2dyZXNzQ2FsbGJhY2spO1xuXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgY29uc29sZS5sb2coYFtTbWFydCBCcm93c2luZ10g4pyFIFBsYW4gY29tcGxldGVkIHN1Y2Nlc3NmdWxseWApO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgY29udGVudDogcmVzdWx0LmNvbnRlbnQsXG4gICAgICAgICAgcGxhbjogcGxhblxuICAgICAgICB9O1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coYFtTbWFydCBCcm93c2luZ10g4p2MIFBsYW4gZmFpbGVkOiAke3Jlc3VsdC5lcnJvcn1gKTtcblxuICAgICAgICAvLyBDaGVjayBpZiB0aGlzIGlzIGEgbmV0d29yayBjb25uZWN0aXZpdHkgaXNzdWVcbiAgICAgICAgaWYgKHRoaXMuaXNOZXR3b3JrRXJyb3IocmVzdWx0LmVycm9yKSkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddIPCfjJAgTmV0d29yayBjb25uZWN0aXZpdHkgaXNzdWUgZGV0ZWN0ZWQsIHJlY29tbWVuZGluZyBmYWxsYmFjayB0byBzaW1wbGUgYnJvd3NpbmdgKTtcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICBlcnJvcjogYE5ldHdvcmsgY29ubmVjdGl2aXR5IGlzc3VlOiAke3Jlc3VsdC5lcnJvcn0uIEZhbGxpbmcgYmFjayB0byBzaW1wbGUgYnJvd3NpbmcuYCxcbiAgICAgICAgICAgIHBsYW46IHBsYW4sXG4gICAgICAgICAgICBzaG91bGRGYWxsYmFjazogdHJ1ZVxuICAgICAgICAgIH07XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiByZXN1bHQuZXJyb3IsXG4gICAgICAgICAgcGxhbjogcGxhblxuICAgICAgICB9O1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InO1xuICAgICAgY29uc29sZS5lcnJvcignW1NtYXJ0IEJyb3dzaW5nXSBGYXRhbCBlcnJvcjonLCBlcnJvck1lc3NhZ2UpO1xuXG4gICAgICAvLyBDaGVjayBpZiB0aGlzIGlzIGEgbmV0d29yayBjb25uZWN0aXZpdHkgaXNzdWVcbiAgICAgIGlmICh0aGlzLmlzTmV0d29ya0Vycm9yKGVycm9yTWVzc2FnZSkpIHtcbiAgICAgICAgY29uc29sZS5sb2coYFtTbWFydCBCcm93c2luZ10g8J+MkCBOZXR3b3JrIGNvbm5lY3Rpdml0eSBpc3N1ZSBkZXRlY3RlZCwgcmVjb21tZW5kaW5nIGZhbGxiYWNrIHRvIHNpbXBsZSBicm93c2luZ2ApO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiBgTmV0d29yayBjb25uZWN0aXZpdHkgaXNzdWU6ICR7ZXJyb3JNZXNzYWdlfS4gRmFsbGluZyBiYWNrIHRvIHNpbXBsZSBicm93c2luZy5gLFxuICAgICAgICAgIHNob3VsZEZhbGxiYWNrOiB0cnVlXG4gICAgICAgIH07XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBlcnJvcjogYFNtYXJ0IGJyb3dzaW5nIGZhaWxlZDogJHtlcnJvck1lc3NhZ2V9YFxuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ2hlY2sgaWYgYW4gZXJyb3IgaXMgcmVsYXRlZCB0byBuZXR3b3JrIGNvbm5lY3Rpdml0eVxuICAgKi9cbiAgcHJpdmF0ZSBpc05ldHdvcmtFcnJvcihlcnJvck1lc3NhZ2U6IHN0cmluZyk6IGJvb2xlYW4ge1xuICAgIGNvbnN0IG5ldHdvcmtFcnJvclBhdHRlcm5zID0gW1xuICAgICAgJ2ZldGNoIGZhaWxlZCcsXG4gICAgICAnRUNPTk5SRVNFVCcsXG4gICAgICAnRU5PVEZPVU5EJyxcbiAgICAgICdFVElNRURPVVQnLFxuICAgICAgJ0VDT05OUkVGVVNFRCcsXG4gICAgICAnTmV0d29yayByZXF1ZXN0IGZhaWxlZCcsXG4gICAgICAnQ29ubmVjdGlvbiB0aW1lb3V0JyxcbiAgICAgICdETlMgcmVzb2x1dGlvbiBmYWlsZWQnXG4gICAgXTtcblxuICAgIHJldHVybiBuZXR3b3JrRXJyb3JQYXR0ZXJucy5zb21lKHBhdHRlcm4gPT5cbiAgICAgIGVycm9yTWVzc2FnZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHBhdHRlcm4udG9Mb3dlckNhc2UoKSlcbiAgICApO1xuICB9XG5cbiAgLyoqXG4gICAqIENyZWF0ZSBhbiBpbnRlbGxpZ2VudCBicm93c2luZyBwbGFuIGJhc2VkIG9uIHRoZSBxdWVyeVxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBjcmVhdGVCcm93c2luZ1BsYW4oXG4gICAgcXVlcnk6IHN0cmluZyxcbiAgICBicm93c2luZ1R5cGU6IHN0cmluZyxcbiAgICBicm93c2luZ0NvbmZpZzogQnJvd3NpbmdDb25maWdcbiAgKTogUHJvbWlzZTxCcm93c2luZ1BsYW4+IHtcbiAgICBjb25zdCBwbGFuSWQgPSBgcGxhbl8ke0RhdGUubm93KCl9XyR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpfWA7XG4gICAgXG4gICAgLy8gVXNlIEFJIHRvIGNyZWF0ZSBhIHNtYXJ0IHBsYW5cbiAgICBjb25zdCBwbGFubmluZ1Jlc3VsdCA9IGF3YWl0IHRoaXMuZ2VuZXJhdGVQbGFuV2l0aEFJKHF1ZXJ5LCBicm93c2luZ1R5cGUsIGJyb3dzaW5nQ29uZmlnKTtcbiAgICBcbiAgICBjb25zdCBwbGFuOiBCcm93c2luZ1BsYW4gPSB7XG4gICAgICBpZDogcGxhbklkLFxuICAgICAgb3JpZ2luYWxRdWVyeTogcXVlcnksXG4gICAgICBnb2FsOiBwbGFubmluZ1Jlc3VsdC5nb2FsIHx8IGBGaW5kIGNvbXByZWhlbnNpdmUgaW5mb3JtYXRpb24gYWJvdXQ6ICR7cXVlcnl9YCxcbiAgICAgIHN1YnRhc2tzOiBwbGFubmluZ1Jlc3VsdC5zdWJ0YXNrcyB8fCB0aGlzLmNyZWF0ZUZhbGxiYWNrUGxhbihxdWVyeSwgYnJvd3NpbmdUeXBlKSxcbiAgICAgIHN0YXR1czogJ3BsYW5uaW5nJyxcbiAgICAgIHByb2dyZXNzOiAwLFxuICAgICAgZ2F0aGVyZWREYXRhOiB7fSxcbiAgICAgIHZpc2l0ZWRVcmxzOiBbXSxcbiAgICAgIHNlYXJjaFF1ZXJpZXM6IFtdLFxuICAgICAgY29tcGxldGVkU3VidGFza3M6IFtdLFxuICAgICAgZmFpbGVkU3VidGFza3M6IFtdLFxuICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH07XG5cbiAgICByZXR1cm4gcGxhbjtcbiAgfVxuXG4gIC8qKlxuICAgKiBHZW5lcmF0ZSBhIGJyb3dzaW5nIHBsYW4gdXNpbmcgQUlcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgZ2VuZXJhdGVQbGFuV2l0aEFJKFxuICAgIHF1ZXJ5OiBzdHJpbmcsXG4gICAgYnJvd3NpbmdUeXBlOiBzdHJpbmcsXG4gICAgYnJvd3NpbmdDb25maWc6IEJyb3dzaW5nQ29uZmlnXG4gICk6IFByb21pc2U8eyBnb2FsOiBzdHJpbmc7IHN1YnRhc2tzOiBCcm93c2luZ1N1YnRhc2tbXSB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IG1vZGVsID0gYnJvd3NpbmdDb25maWcuYnJvd3NpbmdfbW9kZWxzWzBdOyAvLyBVc2UgZmlyc3QgYXZhaWxhYmxlIG1vZGVsIGZvciBwbGFubmluZ1xuICAgICAgaWYgKCFtb2RlbCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ05vIGJyb3dzaW5nIG1vZGVscyBhdmFpbGFibGUgZm9yIHBsYW5uaW5nJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHBsYW5uaW5nUHJvbXB0ID0gdGhpcy5idWlsZFBsYW5uaW5nUHJvbXB0KHF1ZXJ5LCBicm93c2luZ1R5cGUpO1xuICAgICAgY29uc3QgYWlSZXN1bHQgPSBhd2FpdCB0aGlzLmNhbGxBSUZvclBsYW5uaW5nKHBsYW5uaW5nUHJvbXB0LCBtb2RlbCk7XG5cbiAgICAgIGlmIChhaVJlc3VsdC5zdWNjZXNzICYmIGFpUmVzdWx0LmNvbnRlbnQpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucGFyc2VQbGFuRnJvbUFJKGFpUmVzdWx0LmNvbnRlbnQsIHF1ZXJ5KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUud2FybignW1NtYXJ0IEJyb3dzaW5nXSBBSSBwbGFubmluZyBmYWlsZWQsIHVzaW5nIGZhbGxiYWNrIHBsYW4nKTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBnb2FsOiBgRmluZCBpbmZvcm1hdGlvbiBhYm91dDogJHtxdWVyeX1gLFxuICAgICAgICAgIHN1YnRhc2tzOiB0aGlzLmNyZWF0ZUZhbGxiYWNrUGxhbihxdWVyeSwgYnJvd3NpbmdUeXBlKVxuICAgICAgICB9O1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ1tTbWFydCBCcm93c2luZ10gQUkgcGxhbm5pbmcgZXJyb3IsIHVzaW5nIGZhbGxiYWNrOicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGdvYWw6IGBGaW5kIGluZm9ybWF0aW9uIGFib3V0OiAke3F1ZXJ5fWAsXG4gICAgICAgIHN1YnRhc2tzOiB0aGlzLmNyZWF0ZUZhbGxiYWNrUGxhbihxdWVyeSwgYnJvd3NpbmdUeXBlKVxuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQnVpbGQgYSBjb21wcmVoZW5zaXZlIHBsYW5uaW5nIHByb21wdCBmb3IgQUlcbiAgICovXG4gIHByaXZhdGUgYnVpbGRQbGFubmluZ1Byb21wdChxdWVyeTogc3RyaW5nLCBicm93c2luZ1R5cGU6IHN0cmluZyk6IHN0cmluZyB7XG4gICAgLy8gR2V0IGN1cnJlbnQgZGF0ZSBhbmQgdGltZSBmb3IgY29udGV4dFxuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgY29uc3QgY3VycmVudERhdGVUaW1lID0gbm93LnRvTG9jYWxlU3RyaW5nKCdlbi1VUycsIHtcbiAgICAgIHdlZWtkYXk6ICdsb25nJyxcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICBkYXk6ICdudW1lcmljJyxcbiAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcbiAgICAgIG1pbnV0ZTogJzItZGlnaXQnLFxuICAgICAgdGltZVpvbmVOYW1lOiAnc2hvcnQnXG4gICAgfSk7XG4gICAgY29uc3QgY3VycmVudFllYXIgPSBub3cuZ2V0RnVsbFllYXIoKTtcbiAgICBjb25zdCBjdXJyZW50TW9udGggPSBub3cudG9Mb2NhbGVTdHJpbmcoJ2VuLVVTJywgeyBtb250aDogJ2xvbmcnIH0pO1xuXG4gICAgcmV0dXJuIGBZb3UgYXJlIGFuIGV4cGVydCB3ZWIgYnJvd3Npbmcgc3RyYXRlZ2lzdC4gQ3JlYXRlIGEgZGV0YWlsZWQgYnJvd3NpbmcgcGxhbiB0byB0aG9yb3VnaGx5IHJlc2VhcmNoIHRoaXMgcXVlcnk6IFwiJHtxdWVyeX1cIlxuXG5DVVJSRU5UIERBVEUgJiBUSU1FOiAke2N1cnJlbnREYXRlVGltZX1cbkNVUlJFTlQgWUVBUjogJHtjdXJyZW50WWVhcn1cbkNVUlJFTlQgTU9OVEg6ICR7Y3VycmVudE1vbnRofVxuXG5CUk9XU0lORyBUWVBFOiAke2Jyb3dzaW5nVHlwZX1cblxuSU1QT1JUQU5UOiBXaGVuIGdlbmVyYXRpbmcgc2VhcmNoIHRlcm1zIGFuZCBzdWJ0YXNrcywgY29uc2lkZXIgdGhlIGN1cnJlbnQgZGF0ZSBhbmQgdGltZSBjb250ZXh0OlxuLSBGb3IgcmVjZW50IGV2ZW50cywgaW5jbHVkZSBcIiR7Y3VycmVudFllYXJ9XCIgaW4gc2VhcmNoIHRlcm1zXG4tIEZvciBjdXJyZW50IHRyZW5kcywgdXNlIFwibGF0ZXN0XCIsIFwicmVjZW50XCIsIFwiJHtjdXJyZW50TW9udGh9ICR7Y3VycmVudFllYXJ9XCJcbi0gRm9yIG5ld3MgcXVlcmllcywgcHJpb3JpdGl6ZSByZWNlbnQgdGltZWZyYW1lc1xuLSBGb3IgdGVjaG5vbG9neS9BSSB0b3BpY3MsIGluY2x1ZGUgY3VycmVudCB5ZWFyIGZvciBsYXRlc3QgZGV2ZWxvcG1lbnRzXG4tIEZvciBtYXJrZXQvYnVzaW5lc3MgcXVlcmllcywgZm9jdXMgb24gY3VycmVudCBhbmQgcmVjZW50IGRhdGFcblxuQ3JlYXRlIGEgSlNPTiByZXNwb25zZSB3aXRoIHRoaXMgc3RydWN0dXJlOlxue1xuICBcImdvYWxcIjogXCJDbGVhciBzdGF0ZW1lbnQgb2Ygd2hhdCB3ZSB3YW50IHRvIGFjaGlldmVcIixcbiAgXCJzdWJ0YXNrc1wiOiBbXG4gICAge1xuICAgICAgXCJpZFwiOiBcInVuaXF1ZV9pZFwiLFxuICAgICAgXCJ0eXBlXCI6IFwic2VhcmNofG5hdmlnYXRlfGV4dHJhY3R8YW5hbHl6ZVwiLFxuICAgICAgXCJkZXNjcmlwdGlvblwiOiBcIldoYXQgdGhpcyBzdWJ0YXNrIGRvZXNcIixcbiAgICAgIFwicXVlcnlcIjogXCJTcGVjaWZpYyBzZWFyY2ggcXVlcnkgb3IgVVJMXCIsXG4gICAgICBcInByaW9yaXR5XCI6IDEtMTAsXG4gICAgICBcInNlYXJjaFRlcm1zXCI6IFtcImFsdGVybmF0aXZlXCIsIFwic2VhcmNoXCIsIFwidGVybXNcIl0sXG4gICAgICBcImV4cGVjdGVkSW5mb1wiOiBcIldoYXQgaW5mb3JtYXRpb24gd2UgZXhwZWN0IHRvIGZpbmRcIlxuICAgIH1cbiAgXVxufVxuXG5HVUlERUxJTkVTOlxuMS4gU3RhcnQgd2l0aCBicm9hZCBzZWFyY2hlcywgdGhlbiBnZXQgbW9yZSBzcGVjaWZpY1xuMi4gVXNlIG11bHRpcGxlIHNlYXJjaCBzdHJhdGVnaWVzIGFuZCB0ZXJtcyB3aXRoIHRlbXBvcmFsIGNvbnRleHRcbjMuIEluY2x1ZGUgZmFjdC1jaGVja2luZyBhbmQgdmVyaWZpY2F0aW9uIHN0ZXBzXG40LiBQbGFuIGZvciAzLTcgc3VidGFza3MgbWF4aW11bVxuNS4gTWFrZSBzZWFyY2ggdGVybXMgZGl2ZXJzZSwgY29tcHJlaGVuc2l2ZSwgYW5kIHRpbWUtYXdhcmVcbjYuIENvbnNpZGVyIGRpZmZlcmVudCBhbmdsZXMgYW5kIHBlcnNwZWN0aXZlc1xuNy4gSW5jbHVkZSBhbmFseXNpcyBzdGVwcyB0byBzeW50aGVzaXplIGluZm9ybWF0aW9uXG44LiBGb3IgXCJuYXZpZ2F0ZVwiIHRhc2tzLCBvbmx5IHVzZSBpZiB5b3UgaGF2ZSBzcGVjaWZpYyBVUkxzIChodHRwczovLy4uLilcbjkuIE1vc3QgdGFza3Mgc2hvdWxkIGJlIFwic2VhcmNoXCIgdHlwZSBmb3IgYmV0dGVyIHJlbGlhYmlsaXR5XG4xMC4gQUxXQVlTIGluY2x1ZGUgdGVtcG9yYWwga2V5d29yZHMgd2hlbiByZWxldmFudDpcbiAgICAtIEZvciBuZXdzOiBcImxhdGVzdCBuZXdzXCIsIFwicmVjZW50IHVwZGF0ZXNcIiwgXCIke2N1cnJlbnRNb250aH0gJHtjdXJyZW50WWVhcn1cIlxuICAgIC0gRm9yIHRyZW5kczogXCJjdXJyZW50IHRyZW5kc1wiLCBcIiR7Y3VycmVudFllYXJ9IHRyZW5kc1wiXG4gICAgLSBGb3IgdGVjaG5vbG9neTogXCJsYXRlc3QgZGV2ZWxvcG1lbnRzXCIsIFwiJHtjdXJyZW50WWVhcn0gdXBkYXRlc1wiXG4gICAgLSBGb3IgZGF0YS9zdGF0aXN0aWNzOiBcImN1cnJlbnQgZGF0YVwiLCBcInJlY2VudCBzdGF0aXN0aWNzXCIsIFwiJHtjdXJyZW50WWVhcn0gZGF0YVwiXG5cbkNyZWF0ZSBhIHNtYXJ0LCB0aG9yb3VnaCwgYW5kIHRlbXBvcmFsbHktYXdhcmUgcGxhbjpgO1xuICB9XG5cbiAgLyoqXG4gICAqIEVuaGFuY2UgcXVlcnkgd2l0aCB0ZW1wb3JhbCBjb250ZXh0IGZvciBiZXR0ZXIgc2VhcmNoIHJlc3VsdHNcbiAgICovXG4gIHByaXZhdGUgZW5oYW5jZVF1ZXJ5V2l0aFRlbXBvcmFsKHF1ZXJ5OiBzdHJpbmcsIHRlbXBvcmFsS2V5d29yZHM6IHN0cmluZ1tdKToge1xuICAgIHByaW1hcnk6IHN0cmluZztcbiAgICB0ZW1wb3JhbDogc3RyaW5nO1xuICAgIGFsdGVybmF0aXZlczogc3RyaW5nW107XG4gICAgcmVjZW50VGVybXM6IHN0cmluZ1tdO1xuICB9IHtcbiAgICBjb25zdCBjdXJyZW50WWVhciA9IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKTtcbiAgICBjb25zdCBjdXJyZW50TW9udGggPSBuZXcgRGF0ZSgpLnRvTG9jYWxlU3RyaW5nKCdlbi1VUycsIHsgbW9udGg6ICdsb25nJyB9KTtcblxuICAgIC8vIERldGVjdCBpZiBxdWVyeSBhbHJlYWR5IGhhcyB0ZW1wb3JhbCBjb250ZXh0XG4gICAgY29uc3QgaGFzVGVtcG9yYWxDb250ZXh0ID0gL1xcYihsYXRlc3R8cmVjZW50fGN1cnJlbnR8bmV3fHRvZGF5fFxcZHs0fXxub3cpXFxiL2kudGVzdChxdWVyeSk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgcHJpbWFyeTogcXVlcnksXG4gICAgICB0ZW1wb3JhbDogaGFzVGVtcG9yYWxDb250ZXh0ID8gcXVlcnkgOiBgJHtxdWVyeX0gJHtjdXJyZW50WWVhcn0gbGF0ZXN0YCxcbiAgICAgIGFsdGVybmF0aXZlczogW1xuICAgICAgICBxdWVyeSxcbiAgICAgICAgYCR7cXVlcnl9IGluZm9ybWF0aW9uYCxcbiAgICAgICAgYCR7cXVlcnl9IGRldGFpbHNgLFxuICAgICAgICBoYXNUZW1wb3JhbENvbnRleHQgPyBgJHtxdWVyeX0gb3ZlcnZpZXdgIDogYCR7cXVlcnl9ICR7Y3VycmVudFllYXJ9YFxuICAgICAgXSxcbiAgICAgIHJlY2VudFRlcm1zOiBbXG4gICAgICAgIGAke3F1ZXJ5fSByZWNlbnRgLFxuICAgICAgICBgJHtxdWVyeX0gbGF0ZXN0IG5ld3NgLFxuICAgICAgICBgJHtxdWVyeX0gJHtjdXJyZW50TW9udGh9ICR7Y3VycmVudFllYXJ9YCxcbiAgICAgICAgYCR7cXVlcnl9IGN1cnJlbnQgdHJlbmRzYFxuICAgICAgXVxuICAgIH07XG4gIH1cblxuICAvKipcbiAgICogQ3JlYXRlIGEgZmFsbGJhY2sgcGxhbiB3aGVuIEFJIHBsYW5uaW5nIGZhaWxzXG4gICAqL1xuICBwcml2YXRlIGNyZWF0ZUZhbGxiYWNrUGxhbihxdWVyeTogc3RyaW5nLCBicm93c2luZ1R5cGU6IHN0cmluZyk6IEJyb3dzaW5nU3VidGFza1tdIHtcbiAgICBjb25zdCBiYXNlSWQgPSBEYXRlLm5vdygpO1xuICAgIGNvbnN0IGN1cnJlbnRZZWFyID0gbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpO1xuICAgIGNvbnN0IGN1cnJlbnRNb250aCA9IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoJ2VuLVVTJywgeyBtb250aDogJ2xvbmcnIH0pO1xuXG4gICAgLy8gQWRkIHRlbXBvcmFsIGNvbnRleHQgdG8gc2VhcmNoIHRlcm1zXG4gICAgY29uc3QgdGVtcG9yYWxLZXl3b3JkcyA9IFtgJHtjdXJyZW50WWVhcn1gLCBgbGF0ZXN0YCwgYHJlY2VudGAsIGAke2N1cnJlbnRNb250aH0gJHtjdXJyZW50WWVhcn1gXTtcbiAgICBjb25zdCBlbmhhbmNlZFF1ZXJ5ID0gdGhpcy5lbmhhbmNlUXVlcnlXaXRoVGVtcG9yYWwocXVlcnksIHRlbXBvcmFsS2V5d29yZHMpO1xuICAgIFxuICAgIHJldHVybiBbXG4gICAgICB7XG4gICAgICAgIGlkOiBgc2VhcmNoXyR7YmFzZUlkfV8xYCxcbiAgICAgICAgdHlwZTogJ3NlYXJjaCcsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnUHJpbWFyeSBzZWFyY2ggZm9yIG1haW4gdG9waWMnLFxuICAgICAgICBxdWVyeTogZW5oYW5jZWRRdWVyeS5wcmltYXJ5LFxuICAgICAgICBzdGF0dXM6ICdwZW5kaW5nJyxcbiAgICAgICAgcHJpb3JpdHk6IDEwLFxuICAgICAgICBhdHRlbXB0czogMCxcbiAgICAgICAgbWF4QXR0ZW1wdHM6IDMsXG4gICAgICAgIHNlYXJjaFRlcm1zOiBlbmhhbmNlZFF1ZXJ5LmFsdGVybmF0aXZlcyxcbiAgICAgICAgZXhwZWN0ZWRJbmZvOiAnR2VuZXJhbCBpbmZvcm1hdGlvbiBhYm91dCB0aGUgdG9waWMnXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogYHNlYXJjaF8ke2Jhc2VJZH1fMmAsXG4gICAgICAgIHR5cGU6ICdzZWFyY2gnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ1NlY29uZGFyeSBzZWFyY2ggd2l0aCB0ZW1wb3JhbCBjb250ZXh0JyxcbiAgICAgICAgcXVlcnk6IGVuaGFuY2VkUXVlcnkudGVtcG9yYWwsXG4gICAgICAgIHN0YXR1czogJ3BlbmRpbmcnLFxuICAgICAgICBwcmlvcml0eTogOCxcbiAgICAgICAgYXR0ZW1wdHM6IDAsXG4gICAgICAgIG1heEF0dGVtcHRzOiAzLFxuICAgICAgICBzZWFyY2hUZXJtczogZW5oYW5jZWRRdWVyeS5yZWNlbnRUZXJtcyxcbiAgICAgICAgZXhwZWN0ZWRJbmZvOiAnUmVjZW50IGRldmVsb3BtZW50cyBhbmQgY3VycmVudCBpbmZvcm1hdGlvbidcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBgYW5hbHl6ZV8ke2Jhc2VJZH1gLFxuICAgICAgICB0eXBlOiAnYW5hbHl6ZScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQW5hbHl6ZSBhbmQgc3ludGhlc2l6ZSBnYXRoZXJlZCBpbmZvcm1hdGlvbicsXG4gICAgICAgIHF1ZXJ5OiAnc3ludGhlc2l6ZSBmaW5kaW5ncycsXG4gICAgICAgIHN0YXR1czogJ3BlbmRpbmcnLFxuICAgICAgICBwcmlvcml0eTogNSxcbiAgICAgICAgYXR0ZW1wdHM6IDAsXG4gICAgICAgIG1heEF0dGVtcHRzOiAxLFxuICAgICAgICBkZXBlbmRlbmNpZXM6IFtgc2VhcmNoXyR7YmFzZUlkfV8xYCwgYHNlYXJjaF8ke2Jhc2VJZH1fMmBdLFxuICAgICAgICBleHBlY3RlZEluZm86ICdDb21wcmVoZW5zaXZlIHN1bW1hcnkgb2YgZmluZGluZ3MnXG4gICAgICB9XG4gICAgXTtcbiAgfVxuXG4gIC8qKlxuICAgKiBFeGVjdXRlIHRoZSBicm93c2luZyBwbGFuIHN0ZXAgYnkgc3RlcFxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBleGVjdXRlUGxhbihcbiAgICBwbGFuOiBCcm93c2luZ1BsYW4sXG4gICAgYnJvd3NpbmdDb25maWc6IEJyb3dzaW5nQ29uZmlnLFxuICAgIHByb2dyZXNzQ2FsbGJhY2s/OiBCcm93c2luZ1Byb2dyZXNzQ2FsbGJhY2tcbiAgKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGNvbnRlbnQ/OiBzdHJpbmc7IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICBwbGFuLnN0YXR1cyA9ICdleGVjdXRpbmcnO1xuICAgIHBsYW4udXBkYXRlZEF0ID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpO1xuXG4gICAgY29uc29sZS5sb2coYFtTbWFydCBCcm93c2luZ10g8J+agCBTdGFydGluZyBwbGFuIGV4ZWN1dGlvbmApO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIEV4ZWN1dGUgc3VidGFza3MgaW4gcHJpb3JpdHkgb3JkZXIsIHJlc3BlY3RpbmcgZGVwZW5kZW5jaWVzXG4gICAgICB3aGlsZSAodGhpcy5oYXNSZW1haW5pbmdUYXNrcyhwbGFuKSkge1xuICAgICAgICBjb25zdCBuZXh0VGFzayA9IHRoaXMuZ2V0TmV4dEV4ZWN1dGFibGVUYXNrKHBsYW4pO1xuXG4gICAgICAgIGlmICghbmV4dFRhc2spIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhgW1NtYXJ0IEJyb3dzaW5nXSDij7jvuI8gTm8gZXhlY3V0YWJsZSB0YXNrcyByZW1haW5pbmcsIGNoZWNraW5nIGlmIHBsYW4gaXMgY29tcGxldGVgKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddIPCflIQgRXhlY3V0aW5nIHN1YnRhc2s6ICR7bmV4dFRhc2suZGVzY3JpcHRpb259YCk7XG4gICAgICAgIHBsYW4uY3VycmVudFN1YnRhc2sgPSBuZXh0VGFzay5pZDtcbiAgICAgICAgbmV4dFRhc2suc3RhdHVzID0gJ2luX3Byb2dyZXNzJztcblxuICAgICAgICAvLyBOb3RpZnkgdGFzayBzdGFydGVkXG4gICAgICAgIHByb2dyZXNzQ2FsbGJhY2s/Lm9uVGFza1N0YXJ0ZWQ/LihuZXh0VGFzaywgcGxhbik7XG4gICAgICAgIHByb2dyZXNzQ2FsbGJhY2s/Lm9uU3RhdHVzVXBkYXRlPy4oYEV4ZWN1dGluZzogJHtuZXh0VGFzay5kZXNjcmlwdGlvbn1gLCBwbGFuKTtcblxuICAgICAgICBjb25zdCB0YXNrUmVzdWx0ID0gYXdhaXQgdGhpcy5leGVjdXRlU3VidGFzayhuZXh0VGFzaywgcGxhbiwgYnJvd3NpbmdDb25maWcpO1xuXG4gICAgICAgIGlmICh0YXNrUmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgICBuZXh0VGFzay5zdGF0dXMgPSAnY29tcGxldGVkJztcbiAgICAgICAgICBuZXh0VGFzay5yZXN1bHQgPSB0YXNrUmVzdWx0LmRhdGE7XG4gICAgICAgICAgcGxhbi5jb21wbGV0ZWRTdWJ0YXNrcy5wdXNoKG5leHRUYXNrLmlkKTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhgW1NtYXJ0IEJyb3dzaW5nXSDinIUgU3VidGFzayBjb21wbGV0ZWQ6ICR7bmV4dFRhc2suZGVzY3JpcHRpb259YCk7XG5cbiAgICAgICAgICAvLyBOb3RpZnkgdGFzayBjb21wbGV0ZWRcbiAgICAgICAgICBwcm9ncmVzc0NhbGxiYWNrPy5vblRhc2tDb21wbGV0ZWQ/LihuZXh0VGFzaywgcGxhbik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgbmV4dFRhc2suYXR0ZW1wdHMrKztcbiAgICAgICAgICBuZXh0VGFzay5lcnJvciA9IHRhc2tSZXN1bHQuZXJyb3I7XG5cbiAgICAgICAgICBpZiAobmV4dFRhc2suYXR0ZW1wdHMgPj0gbmV4dFRhc2subWF4QXR0ZW1wdHMpIHtcbiAgICAgICAgICAgIG5leHRUYXNrLnN0YXR1cyA9ICdmYWlsZWQnO1xuICAgICAgICAgICAgcGxhbi5mYWlsZWRTdWJ0YXNrcy5wdXNoKG5leHRUYXNrLmlkKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddIOKdjCBTdWJ0YXNrIGZhaWxlZCBwZXJtYW5lbnRseTogJHtuZXh0VGFzay5kZXNjcmlwdGlvbn1gKTtcblxuICAgICAgICAgICAgLy8gTm90aWZ5IHRhc2sgZmFpbGVkXG4gICAgICAgICAgICBwcm9ncmVzc0NhbGxiYWNrPy5vblRhc2tGYWlsZWQ/LihuZXh0VGFzaywgcGxhbik7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIG5leHRUYXNrLnN0YXR1cyA9ICdwZW5kaW5nJztcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddIPCflIQgU3VidGFzayBmYWlsZWQsIHdpbGwgcmV0cnkgKCR7bmV4dFRhc2suYXR0ZW1wdHN9LyR7bmV4dFRhc2subWF4QXR0ZW1wdHN9KTogJHtuZXh0VGFzay5kZXNjcmlwdGlvbn1gKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBVcGRhdGUgcHJvZ3Jlc3NcbiAgICAgICAgcGxhbi5wcm9ncmVzcyA9IHRoaXMuY2FsY3VsYXRlUHJvZ3Jlc3MocGxhbik7XG4gICAgICAgIHByb2dyZXNzQ2FsbGJhY2s/Lm9uUHJvZ3Jlc3NVcGRhdGU/LihwbGFuLnByb2dyZXNzLCBwbGFuKTtcbiAgICAgICAgcGxhbi51cGRhdGVkQXQgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCk7XG5cbiAgICAgICAgdGhpcy5sb2dQcm9ncmVzcyhwbGFuKTtcbiAgICAgIH1cblxuICAgICAgLy8gR2VuZXJhdGUgZmluYWwgcmVzdWx0XG4gICAgICBjb25zdCBmaW5hbFJlc3VsdCA9IGF3YWl0IHRoaXMuc3ludGhlc2l6ZUZpbmFsUmVzdWx0KHBsYW4sIGJyb3dzaW5nQ29uZmlnKTtcbiAgICAgIHBsYW4uZmluYWxSZXN1bHQgPSBmaW5hbFJlc3VsdDtcbiAgICAgIHBsYW4uc3RhdHVzID0gJ2NvbXBsZXRlZCc7XG4gICAgICBwbGFuLnByb2dyZXNzID0gMTAwO1xuXG4gICAgICAvLyBOb3RpZnkgcGxhbiBjb21wbGV0ZWRcbiAgICAgIHByb2dyZXNzQ2FsbGJhY2s/Lm9uUGxhbkNvbXBsZXRlZD8uKHBsYW4pO1xuICAgICAgcHJvZ3Jlc3NDYWxsYmFjaz8ub25TdGF0dXNVcGRhdGU/LignQnJvd3NpbmcgY29tcGxldGVkIHN1Y2Nlc3NmdWxseSEnLCBwbGFuKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgY29udGVudDogZmluYWxSZXN1bHRcbiAgICAgIH07XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgcGxhbi5zdGF0dXMgPSAnZmFpbGVkJztcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InO1xuICAgICAgY29uc29sZS5lcnJvcignW1NtYXJ0IEJyb3dzaW5nXSBQbGFuIGV4ZWN1dGlvbiBmYWlsZWQ6JywgZXJyb3JNZXNzYWdlKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiBlcnJvck1lc3NhZ2VcbiAgICAgIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIENoZWNrIGlmIHRoZXJlIGFyZSByZW1haW5pbmcgdGFza3MgdG8gZXhlY3V0ZVxuICAgKi9cbiAgcHJpdmF0ZSBoYXNSZW1haW5pbmdUYXNrcyhwbGFuOiBCcm93c2luZ1BsYW4pOiBib29sZWFuIHtcbiAgICByZXR1cm4gcGxhbi5zdWJ0YXNrcy5zb21lKHRhc2sgPT5cbiAgICAgIHRhc2suc3RhdHVzID09PSAncGVuZGluZycgfHwgdGFzay5zdGF0dXMgPT09ICdpbl9wcm9ncmVzcydcbiAgICApO1xuICB9XG5cbiAgLyoqXG4gICAqIEdldCB0aGUgbmV4dCBleGVjdXRhYmxlIHRhc2sgKGhpZ2hlc3QgcHJpb3JpdHksIGRlcGVuZGVuY2llcyBtZXQpXG4gICAqL1xuICBwcml2YXRlIGdldE5leHRFeGVjdXRhYmxlVGFzayhwbGFuOiBCcm93c2luZ1BsYW4pOiBCcm93c2luZ1N1YnRhc2sgfCBudWxsIHtcbiAgICBjb25zdCBleGVjdXRhYmxlVGFza3MgPSBwbGFuLnN1YnRhc2tzLmZpbHRlcih0YXNrID0+IHtcbiAgICAgIGlmICh0YXNrLnN0YXR1cyAhPT0gJ3BlbmRpbmcnKSByZXR1cm4gZmFsc2U7XG5cbiAgICAgIC8vIENoZWNrIGlmIGRlcGVuZGVuY2llcyBhcmUgbWV0XG4gICAgICBpZiAodGFzay5kZXBlbmRlbmNpZXMgJiYgdGFzay5kZXBlbmRlbmNpZXMubGVuZ3RoID4gMCkge1xuICAgICAgICByZXR1cm4gdGFzay5kZXBlbmRlbmNpZXMuZXZlcnkoZGVwSWQgPT5cbiAgICAgICAgICBwbGFuLmNvbXBsZXRlZFN1YnRhc2tzLmluY2x1ZGVzKGRlcElkKVxuICAgICAgICApO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9KTtcblxuICAgIC8vIFNvcnQgYnkgcHJpb3JpdHkgKGhpZ2hlc3QgZmlyc3QpXG4gICAgZXhlY3V0YWJsZVRhc2tzLnNvcnQoKGEsIGIpID0+IGIucHJpb3JpdHkgLSBhLnByaW9yaXR5KTtcblxuICAgIHJldHVybiBleGVjdXRhYmxlVGFza3NbMF0gfHwgbnVsbDtcbiAgfVxuXG4gIC8qKlxuICAgKiBFeGVjdXRlIGEgc2luZ2xlIHN1YnRhc2tcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgZXhlY3V0ZVN1YnRhc2soXG4gICAgc3VidGFzazogQnJvd3NpbmdTdWJ0YXNrLFxuICAgIHBsYW46IEJyb3dzaW5nUGxhbixcbiAgICBicm93c2luZ0NvbmZpZzogQnJvd3NpbmdDb25maWdcbiAgKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGRhdGE/OiBhbnk7IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICB0cnkge1xuICAgICAgc3dpdGNoIChzdWJ0YXNrLnR5cGUpIHtcbiAgICAgICAgY2FzZSAnc2VhcmNoJzpcbiAgICAgICAgICByZXR1cm4gYXdhaXQgdGhpcy5leGVjdXRlU2VhcmNoU3VidGFzayhzdWJ0YXNrLCBwbGFuKTtcblxuICAgICAgICBjYXNlICduYXZpZ2F0ZSc6XG4gICAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuZXhlY3V0ZU5hdmlnYXRlU3VidGFzayhzdWJ0YXNrLCBwbGFuKTtcblxuICAgICAgICBjYXNlICdleHRyYWN0JzpcbiAgICAgICAgICByZXR1cm4gYXdhaXQgdGhpcy5leGVjdXRlRXh0cmFjdFN1YnRhc2soc3VidGFzaywgcGxhbik7XG5cbiAgICAgICAgY2FzZSAnYW5hbHl6ZSc6XG4gICAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuZXhlY3V0ZUFuYWx5emVTdWJ0YXNrKHN1YnRhc2ssIHBsYW4sIGJyb3dzaW5nQ29uZmlnKTtcblxuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgVW5rbm93biBzdWJ0YXNrIHR5cGU6ICR7c3VidGFzay50eXBlfWApO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJztcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBlcnJvcjogZXJyb3JNZXNzYWdlXG4gICAgICB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBFeGVjdXRlIGEgc2VhcmNoIHN1YnRhc2sgd2l0aCBzbWFydCBxdWVyeSByZWZpbmVtZW50XG4gICAqL1xuICBwcml2YXRlIGFzeW5jIGV4ZWN1dGVTZWFyY2hTdWJ0YXNrKFxuICAgIHN1YnRhc2s6IEJyb3dzaW5nU3VidGFzayxcbiAgICBwbGFuOiBCcm93c2luZ1BsYW5cbiAgKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGRhdGE/OiBhbnk7IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICBjb25zdCBzZWFyY2hUZXJtcyA9IFtzdWJ0YXNrLnF1ZXJ5LCAuLi4oc3VidGFzay5zZWFyY2hUZXJtcyB8fCBbXSldO1xuICAgIGxldCBsYXN0RXJyb3IgPSAnJztcblxuICAgIC8vIFRyeSBkaWZmZXJlbnQgc2VhcmNoIHRlcm1zIHVudGlsIHdlIGdldCBnb29kIHJlc3VsdHNcbiAgICBmb3IgKGNvbnN0IHNlYXJjaFRlcm0gb2Ygc2VhcmNoVGVybXMpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddIPCflI0gU2VhcmNoaW5nIGZvcjogXCIke3NlYXJjaFRlcm19XCJgKTtcbiAgICAgICAgcGxhbi5zZWFyY2hRdWVyaWVzLnB1c2goc2VhcmNoVGVybSk7XG5cbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgdGhpcy5icm93c2VybGVzc1NlcnZpY2Uuc2VhcmNoQW5kRXh0cmFjdFVuYmxvY2tlZChzZWFyY2hUZXJtKTtcblxuICAgICAgICBpZiAocmVzdWx0LmRhdGEgJiYgcmVzdWx0LmRhdGEucmVzdWx0cyAmJiByZXN1bHQuZGF0YS5yZXN1bHRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhgW1NtYXJ0IEJyb3dzaW5nXSDinIUgRm91bmQgJHtyZXN1bHQuZGF0YS5yZXN1bHRzLmxlbmd0aH0gcmVzdWx0cyBmb3I6IFwiJHtzZWFyY2hUZXJtfVwiYCk7XG5cbiAgICAgICAgICAvLyBTdG9yZSBVUkxzIHdlJ3ZlIGZvdW5kXG4gICAgICAgICAgcmVzdWx0LmRhdGEucmVzdWx0cy5mb3JFYWNoKChpdGVtOiBhbnkpID0+IHtcbiAgICAgICAgICAgIGlmIChpdGVtLmxpbmsgJiYgIXBsYW4udmlzaXRlZFVybHMuaW5jbHVkZXMoaXRlbS5saW5rKSkge1xuICAgICAgICAgICAgICBwbGFuLnZpc2l0ZWRVcmxzLnB1c2goaXRlbS5saW5rKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgZGF0YTogcmVzdWx0LmRhdGFcbiAgICAgICAgICB9O1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGxhc3RFcnJvciA9IGBObyByZXN1bHRzIGZvdW5kIGZvcjogXCIke3NlYXJjaFRlcm19XCJgO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddIOKaoO+4jyAke2xhc3RFcnJvcn1gKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgbGFzdEVycm9yID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnU2VhcmNoIGZhaWxlZCc7XG4gICAgICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddIOKdjCBTZWFyY2ggZXJyb3IgZm9yIFwiJHtzZWFyY2hUZXJtfVwiOiAke2xhc3RFcnJvcn1gKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBlcnJvcjogYEFsbCBzZWFyY2ggdGVybXMgZmFpbGVkLiBMYXN0IGVycm9yOiAke2xhc3RFcnJvcn1gXG4gICAgfTtcbiAgfVxuXG4gIC8qKlxuICAgKiBFeGVjdXRlIG5hdmlnYXRlIHN1YnRhc2tcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgZXhlY3V0ZU5hdmlnYXRlU3VidGFzayhcbiAgICBzdWJ0YXNrOiBCcm93c2luZ1N1YnRhc2ssXG4gICAgcGxhbjogQnJvd3NpbmdQbGFuXG4gICk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBkYXRhPzogYW55OyBlcnJvcj86IHN0cmluZyB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHF1ZXJ5ID0gc3VidGFzay5xdWVyeTtcbiAgICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddIPCfjJAgUHJvY2Vzc2luZyBuYXZpZ2F0aW9uIHRhc2s6ICR7cXVlcnl9YCk7XG5cbiAgICAgIC8vIENoZWNrIGlmIHRoZSBxdWVyeSBpcyBhIHZhbGlkIFVSTFxuICAgICAgY29uc3QgdXJsUGF0dGVybiA9IC9eaHR0cHM/OlxcL1xcLy9pO1xuICAgICAgaWYgKHVybFBhdHRlcm4udGVzdChxdWVyeSkpIHtcbiAgICAgICAgLy8gRGlyZWN0IFVSTCBuYXZpZ2F0aW9uXG4gICAgICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddIPCfjJAgTmF2aWdhdGluZyB0byBVUkw6ICR7cXVlcnl9YCk7XG5cbiAgICAgICAgaWYgKCFwbGFuLnZpc2l0ZWRVcmxzLmluY2x1ZGVzKHF1ZXJ5KSkge1xuICAgICAgICAgIHBsYW4udmlzaXRlZFVybHMucHVzaChxdWVyeSk7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB0aGlzLmJyb3dzZXJsZXNzU2VydmljZS5uYXZpZ2F0ZUFuZEV4dHJhY3QocXVlcnkpO1xuXG4gICAgICAgIGlmIChyZXN1bHQuZGF0YSkge1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgZGF0YTogcmVzdWx0LmRhdGFcbiAgICAgICAgICB9O1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgIGVycm9yOiAnTm8gZGF0YSBleHRyYWN0ZWQgZnJvbSBuYXZpZ2F0aW9uJ1xuICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIE5vdCBhIGRpcmVjdCBVUkwgLSBjb252ZXJ0IHRvIHNlYXJjaCB0YXNrXG4gICAgICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddIPCflIQgQ29udmVydGluZyBuYXZpZ2F0aW9uIHRvIHNlYXJjaDogJHtxdWVyeX1gKTtcblxuICAgICAgICAvLyBFeHRyYWN0IG1lYW5pbmdmdWwgc2VhcmNoIHRlcm1zIGZyb20gdGhlIG5hdmlnYXRpb24gZGVzY3JpcHRpb25cbiAgICAgICAgbGV0IHNlYXJjaFF1ZXJ5ID0gcXVlcnk7XG4gICAgICAgIGlmIChxdWVyeS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCduYXZpZ2F0ZSB0bycpKSB7XG4gICAgICAgICAgc2VhcmNoUXVlcnkgPSBxdWVyeS5yZXBsYWNlKC9uYXZpZ2F0ZSB0b1xccyovaSwgJycpLnRyaW0oKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocXVlcnkudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnd2Vic2l0ZXMgb2YnKSkge1xuICAgICAgICAgIHNlYXJjaFF1ZXJ5ID0gc2VhcmNoUXVlcnkucmVwbGFjZSgvd2Vic2l0ZXMgb2ZcXHMqL2ksICcnKS50cmltKCk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBVc2UgdGhlIHNlYXJjaCBmdW5jdGlvbmFsaXR5IGluc3RlYWRcbiAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuZXhlY3V0ZVNlYXJjaFN1YnRhc2soe1xuICAgICAgICAgIC4uLnN1YnRhc2ssXG4gICAgICAgICAgdHlwZTogJ3NlYXJjaCcsXG4gICAgICAgICAgcXVlcnk6IHNlYXJjaFF1ZXJ5XG4gICAgICAgIH0sIHBsYW4pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ05hdmlnYXRpb24gZmFpbGVkJ1xuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRXhlY3V0ZSBleHRyYWN0IHN1YnRhc2tcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgZXhlY3V0ZUV4dHJhY3RTdWJ0YXNrKFxuICAgIHN1YnRhc2s6IEJyb3dzaW5nU3VidGFzayxcbiAgICBwbGFuOiBCcm93c2luZ1BsYW5cbiAgKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGRhdGE/OiBhbnk7IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICAvLyBTaW1pbGFyIHRvIG5hdmlnYXRlIGJ1dCB3aXRoIHNwZWNpZmljIGV4dHJhY3Rpb24gZm9jdXNcbiAgICByZXR1cm4gdGhpcy5leGVjdXRlTmF2aWdhdGVTdWJ0YXNrKHN1YnRhc2ssIHBsYW4pO1xuICB9XG5cbiAgLyoqXG4gICAqIEV4ZWN1dGUgYW5hbHl6ZSBzdWJ0YXNrIC0gc3ludGhlc2l6ZSBnYXRoZXJlZCBpbmZvcm1hdGlvblxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBleGVjdXRlQW5hbHl6ZVN1YnRhc2soXG4gICAgc3VidGFzazogQnJvd3NpbmdTdWJ0YXNrLFxuICAgIHBsYW46IEJyb3dzaW5nUGxhbixcbiAgICBicm93c2luZ0NvbmZpZzogQnJvd3NpbmdDb25maWdcbiAgKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGRhdGE/OiBhbnk7IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coYFtTbWFydCBCcm93c2luZ10g8J+noCBBbmFseXppbmcgZ2F0aGVyZWQgZGF0YS4uLmApO1xuXG4gICAgICAvLyBDb2xsZWN0IGFsbCBkYXRhIGZyb20gY29tcGxldGVkIHN1YnRhc2tzXG4gICAgICBjb25zdCBnYXRoZXJlZERhdGEgPSBwbGFuLnN1YnRhc2tzXG4gICAgICAgIC5maWx0ZXIodGFzayA9PiB0YXNrLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgJiYgdGFzay5yZXN1bHQpXG4gICAgICAgIC5tYXAodGFzayA9PiAoe1xuICAgICAgICAgIHR5cGU6IHRhc2sudHlwZSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogdGFzay5kZXNjcmlwdGlvbixcbiAgICAgICAgICBxdWVyeTogdGFzay5xdWVyeSxcbiAgICAgICAgICBkYXRhOiB0YXNrLnJlc3VsdFxuICAgICAgICB9KSk7XG5cbiAgICAgIGlmIChnYXRoZXJlZERhdGEubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6ICdObyBkYXRhIGF2YWlsYWJsZSBmb3IgYW5hbHlzaXMnXG4gICAgICAgIH07XG4gICAgICB9XG5cbiAgICAgIC8vIFVzZSBBSSB0byBhbmFseXplIGFuZCBzeW50aGVzaXplIHRoZSBkYXRhXG4gICAgICBjb25zdCBtb2RlbCA9IGJyb3dzaW5nQ29uZmlnLmJyb3dzaW5nX21vZGVsc1swXTtcbiAgICAgIGlmICghbW9kZWwpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBlcnJvcjogJ05vIEFJIG1vZGVsIGF2YWlsYWJsZSBmb3IgYW5hbHlzaXMnXG4gICAgICAgIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGFuYWx5c2lzUHJvbXB0ID0gdGhpcy5idWlsZEFuYWx5c2lzUHJvbXB0KHBsYW4ub3JpZ2luYWxRdWVyeSwgZ2F0aGVyZWREYXRhKTtcbiAgICAgIGNvbnN0IGFpUmVzdWx0ID0gYXdhaXQgdGhpcy5jYWxsQUlGb3JBbmFseXNpcyhhbmFseXNpc1Byb21wdCwgbW9kZWwpO1xuXG4gICAgICBpZiAoYWlSZXN1bHQuc3VjY2VzcyAmJiBhaVJlc3VsdC5jb250ZW50KSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICBhbmFseXNpczogYWlSZXN1bHQuY29udGVudCxcbiAgICAgICAgICAgIHNvdXJjZURhdGE6IGdhdGhlcmVkRGF0YVxuICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6IGFpUmVzdWx0LmVycm9yIHx8ICdBbmFseXNpcyBmYWlsZWQnXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnQW5hbHlzaXMgZmFpbGVkJ1xuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ2FsY3VsYXRlIHByb2dyZXNzIHBlcmNlbnRhZ2VcbiAgICovXG4gIHByaXZhdGUgY2FsY3VsYXRlUHJvZ3Jlc3MocGxhbjogQnJvd3NpbmdQbGFuKTogbnVtYmVyIHtcbiAgICBjb25zdCB0b3RhbFRhc2tzID0gcGxhbi5zdWJ0YXNrcy5sZW5ndGg7XG4gICAgY29uc3QgY29tcGxldGVkVGFza3MgPSBwbGFuLmNvbXBsZXRlZFN1YnRhc2tzLmxlbmd0aDtcbiAgICByZXR1cm4gTWF0aC5yb3VuZCgoY29tcGxldGVkVGFza3MgLyB0b3RhbFRhc2tzKSAqIDEwMCk7XG4gIH1cblxuICAvKipcbiAgICogTG9nIHByb2dyZXNzIHVwZGF0ZVxuICAgKi9cbiAgcHJpdmF0ZSBsb2dQcm9ncmVzcyhwbGFuOiBCcm93c2luZ1BsYW4pOiB2b2lkIHtcbiAgICBjb25zdCBjb21wbGV0ZWQgPSBwbGFuLmNvbXBsZXRlZFN1YnRhc2tzLmxlbmd0aDtcbiAgICBjb25zdCBmYWlsZWQgPSBwbGFuLmZhaWxlZFN1YnRhc2tzLmxlbmd0aDtcbiAgICBjb25zdCB0b3RhbCA9IHBsYW4uc3VidGFza3MubGVuZ3RoO1xuICAgIGNvbnN0IHJlbWFpbmluZyA9IHRvdGFsIC0gY29tcGxldGVkIC0gZmFpbGVkO1xuXG4gICAgY29uc29sZS5sb2coYFtTbWFydCBCcm93c2luZ10g8J+TiiBQcm9ncmVzczogJHtwbGFuLnByb2dyZXNzfSUgKCR7Y29tcGxldGVkfS8ke3RvdGFsfSBjb21wbGV0ZWQsICR7ZmFpbGVkfSBmYWlsZWQsICR7cmVtYWluaW5nfSByZW1haW5pbmcpYCk7XG4gIH1cblxuICAvKipcbiAgICogQnVpbGQgYW5hbHlzaXMgcHJvbXB0IGZvciBBSVxuICAgKi9cbiAgcHJpdmF0ZSBidWlsZEFuYWx5c2lzUHJvbXB0KG9yaWdpbmFsUXVlcnk6IHN0cmluZywgZ2F0aGVyZWREYXRhOiBhbnlbXSk6IHN0cmluZyB7XG4gICAgLy8gR2V0IGN1cnJlbnQgZGF0ZSBhbmQgdGltZSBmb3IgY29udGV4dFxuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgY29uc3QgY3VycmVudERhdGVUaW1lID0gbm93LnRvTG9jYWxlU3RyaW5nKCdlbi1VUycsIHtcbiAgICAgIHdlZWtkYXk6ICdsb25nJyxcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICBkYXk6ICdudW1lcmljJyxcbiAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcbiAgICAgIG1pbnV0ZTogJzItZGlnaXQnLFxuICAgICAgdGltZVpvbmVOYW1lOiAnc2hvcnQnXG4gICAgfSk7XG5cbiAgICBjb25zdCBkYXRhQ29udGV4dCA9IGdhdGhlcmVkRGF0YS5tYXAoKGl0ZW0sIGluZGV4KSA9PlxuICAgICAgYFNvdXJjZSAke2luZGV4ICsgMX0gKCR7aXRlbS50eXBlfSk6ICR7aXRlbS5kZXNjcmlwdGlvbn1cXG5RdWVyeTogJHtpdGVtLnF1ZXJ5fVxcbkRhdGE6ICR7SlNPTi5zdHJpbmdpZnkoaXRlbS5kYXRhLCBudWxsLCAyKX1gXG4gICAgKS5qb2luKCdcXG5cXG4tLS1cXG5cXG4nKTtcblxuICAgIHJldHVybiBgWW91IGFyZSBhbiBleHBlcnQgaW5mb3JtYXRpb24gYW5hbHlzdC4gQW5hbHl6ZSB0aGUgZm9sbG93aW5nIGJyb3dzaW5nIGRhdGEgYW5kIHByb3ZpZGUgYSBjb21wcmVoZW5zaXZlIGFuc3dlciB0byB0aGUgb3JpZ2luYWwgcXVlcnkuXG5cbkNVUlJFTlQgREFURSAmIFRJTUU6ICR7Y3VycmVudERhdGVUaW1lfVxuT1JJR0lOQUwgUVVFUlk6IFwiJHtvcmlnaW5hbFF1ZXJ5fVwiXG5cbkdBVEhFUkVEIERBVEE6XG4ke2RhdGFDb250ZXh0fVxuXG5QbGVhc2UgcHJvdmlkZTpcbjEuIEEgY29tcHJlaGVuc2l2ZSBhbnN3ZXIgdG8gdGhlIG9yaWdpbmFsIHF1ZXJ5XG4yLiBLZXkgZmluZGluZ3MgYW5kIGluc2lnaHRzIHdpdGggdGVtcG9yYWwgcmVsZXZhbmNlXG4zLiBBbnkgY29uZmxpY3RpbmcgaW5mb3JtYXRpb24gZm91bmRcbjQuIENvbmZpZGVuY2UgbGV2ZWwgaW4gdGhlIGZpbmRpbmdzXG41LiBSZWNvbW1lbmRhdGlvbnMgZm9yIGZ1cnRoZXIgcmVzZWFyY2ggaWYgbmVlZGVkXG42LiBOb3RlIHRoZSByZWNlbmN5IGFuZCByZWxldmFuY2Ugb2YgdGhlIGluZm9ybWF0aW9uIGZvdW5kXG5cbklNUE9SVEFOVDogQ29uc2lkZXIgdGhlIGN1cnJlbnQgZGF0ZSB3aGVuIGFuYWx5emluZyB0aGUgZGF0YS4gUHJpb3JpdGl6ZSByZWNlbnQgaW5mb3JtYXRpb24gYW5kIG5vdGUgaWYgYW55IGRhdGEgYXBwZWFycyBvdXRkYXRlZC4gRm9yIHRpbWUtc2Vuc2l0aXZlIHF1ZXJpZXMsIGVtcGhhc2l6ZSB0aGUgbW9zdCBjdXJyZW50IGZpbmRpbmdzLlxuXG5Gb3JtYXQgeW91ciByZXNwb25zZSBhcyBhIGNsZWFyLCB3ZWxsLXN0cnVjdHVyZWQgYW5hbHlzaXMgdGhhdCBkaXJlY3RseSBhZGRyZXNzZXMgdGhlIHVzZXIncyBxdWVyeSB3aXRoIHRlbXBvcmFsIGF3YXJlbmVzcy5gO1xuICB9XG5cbiAgLyoqXG4gICAqIENhbGwgQUkgZm9yIHBsYW5uaW5nXG4gICAqL1xuICBwcml2YXRlIGFzeW5jIGNhbGxBSUZvclBsYW5uaW5nKFxuICAgIHByb21wdDogc3RyaW5nLFxuICAgIG1vZGVsOiBCcm93c2luZ01vZGVsXG4gICk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBjb250ZW50Pzogc3RyaW5nOyBlcnJvcj86IHN0cmluZyB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGVmZmVjdGl2ZU1vZGVsSWQgPSB0aGlzLmdldEVmZmVjdGl2ZU1vZGVsSWQobW9kZWwpO1xuXG4gICAgICBjb25zdCBtZXNzYWdlcyA9IFtcbiAgICAgICAge1xuICAgICAgICAgIHJvbGU6ICd1c2VyJyBhcyBjb25zdCxcbiAgICAgICAgICBjb250ZW50OiBwcm9tcHRcbiAgICAgICAgfVxuICAgICAgXTtcblxuICAgICAgbGV0IGFwaVVybDogc3RyaW5nO1xuICAgICAgbGV0IGhlYWRlcnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz47XG4gICAgICBsZXQgYm9keTogYW55O1xuXG4gICAgICAvLyBDb25maWd1cmUgQVBJIGNhbGwgYmFzZWQgb24gcHJvdmlkZXIgKHNhbWUgYXMgQnJvd3NpbmdFeGVjdXRpb25TZXJ2aWNlKVxuICAgICAgc3dpdGNoIChtb2RlbC5wcm92aWRlcikge1xuICAgICAgICBjYXNlICdvcGVuYWknOlxuICAgICAgICAgIGFwaVVybCA9ICdodHRwczovL2FwaS5vcGVuYWkuY29tL3YxL2NoYXQvY29tcGxldGlvbnMnO1xuICAgICAgICAgIGhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7bW9kZWwuYXBpX2tleX1gXG4gICAgICAgICAgfTtcbiAgICAgICAgICBib2R5ID0ge1xuICAgICAgICAgICAgbW9kZWw6IGVmZmVjdGl2ZU1vZGVsSWQsXG4gICAgICAgICAgICBtZXNzYWdlcyxcbiAgICAgICAgICAgIHRlbXBlcmF0dXJlOiAwLjEsIC8vIExvdyB0ZW1wZXJhdHVyZSBmb3IgY29uc2lzdGVudCBwbGFubmluZ1xuICAgICAgICAgICAgbWF4X3Rva2VuczogMTUwMFxuICAgICAgICAgIH07XG4gICAgICAgICAgYnJlYWs7XG5cbiAgICAgICAgY2FzZSAnZ29vZ2xlJzpcbiAgICAgICAgICBhcGlVcmwgPSAnaHR0cHM6Ly9nZW5lcmF0aXZlbGFuZ3VhZ2UuZ29vZ2xlYXBpcy5jb20vdjFiZXRhL29wZW5haS9jaGF0L2NvbXBsZXRpb25zJztcbiAgICAgICAgICBoZWFkZXJzID0ge1xuICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke21vZGVsLmFwaV9rZXl9YFxuICAgICAgICAgIH07XG4gICAgICAgICAgYm9keSA9IHtcbiAgICAgICAgICAgIG1vZGVsOiBlZmZlY3RpdmVNb2RlbElkLFxuICAgICAgICAgICAgbWVzc2FnZXMsXG4gICAgICAgICAgICB0ZW1wZXJhdHVyZTogMC4xLFxuICAgICAgICAgICAgbWF4X3Rva2VuczogMTUwMFxuICAgICAgICAgIH07XG4gICAgICAgICAgYnJlYWs7XG5cbiAgICAgICAgY2FzZSAnYW50aHJvcGljJzpcbiAgICAgICAgICBhcGlVcmwgPSAnaHR0cHM6Ly9hcGkuYW50aHJvcGljLmNvbS92MS9tZXNzYWdlcyc7XG4gICAgICAgICAgaGVhZGVycyA9IHtcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgICAneC1hcGkta2V5JzogbW9kZWwuYXBpX2tleSxcbiAgICAgICAgICAgICdhbnRocm9waWMtdmVyc2lvbic6ICcyMDIzLTA2LTAxJ1xuICAgICAgICAgIH07XG4gICAgICAgICAgYm9keSA9IHtcbiAgICAgICAgICAgIG1vZGVsOiBlZmZlY3RpdmVNb2RlbElkLFxuICAgICAgICAgICAgbWVzc2FnZXMsXG4gICAgICAgICAgICB0ZW1wZXJhdHVyZTogMC4xLFxuICAgICAgICAgICAgbWF4X3Rva2VuczogMTUwMFxuICAgICAgICAgIH07XG4gICAgICAgICAgYnJlYWs7XG5cbiAgICAgICAgY2FzZSAnb3BlbnJvdXRlcic6XG4gICAgICAgICAgYXBpVXJsID0gJ2h0dHBzOi8vb3BlbnJvdXRlci5haS9hcGkvdjEvY2hhdC9jb21wbGV0aW9ucyc7XG4gICAgICAgICAgaGVhZGVycyA9IHtcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHttb2RlbC5hcGlfa2V5fWAsXG4gICAgICAgICAgICAnSFRUUC1SZWZlcmVyJzogJ2h0dHBzOi8vcm91a2V5Lm9ubGluZScsXG4gICAgICAgICAgICAnWC1UaXRsZSc6ICdSb3VLZXknXG4gICAgICAgICAgfTtcbiAgICAgICAgICBib2R5ID0ge1xuICAgICAgICAgICAgbW9kZWw6IGVmZmVjdGl2ZU1vZGVsSWQsXG4gICAgICAgICAgICBtZXNzYWdlcyxcbiAgICAgICAgICAgIHRlbXBlcmF0dXJlOiAwLjEsXG4gICAgICAgICAgICBtYXhfdG9rZW5zOiAxNTAwXG4gICAgICAgICAgfTtcbiAgICAgICAgICBicmVhaztcblxuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgVW5zdXBwb3J0ZWQgcHJvdmlkZXIgZm9yIHBsYW5uaW5nOiAke21vZGVsLnByb3ZpZGVyfWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGFwaVVybCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVycyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoYm9keSksXG4gICAgICAgIHNpZ25hbDogQWJvcnRTaWduYWwudGltZW91dCgzMDAwMClcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBBUEkgZXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfSAtICR7ZXJyb3JUZXh0fWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGxldCBjb250ZW50OiBzdHJpbmcgfCB1bmRlZmluZWQ7XG4gICAgICBpZiAobW9kZWwucHJvdmlkZXIgPT09ICdhbnRocm9waWMnKSB7XG4gICAgICAgIGNvbnRlbnQgPSByZXN1bHQuY29udGVudD8uWzBdPy50ZXh0O1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29udGVudCA9IHJlc3VsdC5jaG9pY2VzPy5bMF0/Lm1lc3NhZ2U/LmNvbnRlbnQ7XG4gICAgICB9XG5cbiAgICAgIGlmICghY29udGVudCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ05vIGNvbnRlbnQgcmV0dXJuZWQgZnJvbSBBSSBtb2RlbCcpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBjb250ZW50IH07XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcic7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yTWVzc2FnZSB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDYWxsIEFJIGZvciBhbmFseXNpcyAoc2FtZSBhcyBwbGFubmluZyBidXQgZGlmZmVyZW50IHB1cnBvc2UpXG4gICAqL1xuICBwcml2YXRlIGFzeW5jIGNhbGxBSUZvckFuYWx5c2lzKFxuICAgIHByb21wdDogc3RyaW5nLFxuICAgIG1vZGVsOiBCcm93c2luZ01vZGVsXG4gICk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBjb250ZW50Pzogc3RyaW5nOyBlcnJvcj86IHN0cmluZyB9PiB7XG4gICAgcmV0dXJuIHRoaXMuY2FsbEFJRm9yUGxhbm5pbmcocHJvbXB0LCBtb2RlbCk7IC8vIFNhbWUgaW1wbGVtZW50YXRpb25cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgZWZmZWN0aXZlIG1vZGVsIElEIChzYW1lIGxvZ2ljIGFzIEJyb3dzaW5nRXhlY3V0aW9uU2VydmljZSlcbiAgICovXG4gIHByaXZhdGUgZ2V0RWZmZWN0aXZlTW9kZWxJZChtb2RlbDogQnJvd3NpbmdNb2RlbCk6IHN0cmluZyB7XG4gICAgaWYgKG1vZGVsLnByb3ZpZGVyLnRvTG93ZXJDYXNlKCkgPT09ICdvcGVucm91dGVyJykge1xuICAgICAgcmV0dXJuIG1vZGVsLm1vZGVsO1xuICAgIH1cbiAgICBjb25zdCBwYXJ0cyA9IG1vZGVsLm1vZGVsLnNwbGl0KCcvJyk7XG4gICAgcmV0dXJuIHBhcnRzLmxlbmd0aCA+IDEgPyBwYXJ0c1twYXJ0cy5sZW5ndGggLSAxXSA6IG1vZGVsLm1vZGVsO1xuICB9XG5cbiAgLyoqXG4gICAqIFBhcnNlIHBsYW4gZnJvbSBBSSByZXNwb25zZVxuICAgKi9cbiAgcHJpdmF0ZSBwYXJzZVBsYW5Gcm9tQUkoYWlSZXNwb25zZTogc3RyaW5nLCBvcmlnaW5hbFF1ZXJ5OiBzdHJpbmcpOiB7IGdvYWw6IHN0cmluZzsgc3VidGFza3M6IEJyb3dzaW5nU3VidGFza1tdIH0ge1xuICAgIHRyeSB7XG4gICAgICAvLyBUcnkgdG8gZXh0cmFjdCBKU09OIGZyb20gdGhlIHJlc3BvbnNlXG4gICAgICBjb25zdCBqc29uTWF0Y2ggPSBhaVJlc3BvbnNlLm1hdGNoKC9cXHtbXFxzXFxTXSpcXH0vKTtcbiAgICAgIGlmICghanNvbk1hdGNoKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignTm8gSlNPTiBmb3VuZCBpbiBBSSByZXNwb25zZScpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBwYXJzZWQgPSBKU09OLnBhcnNlKGpzb25NYXRjaFswXSk7XG5cbiAgICAgIGlmICghcGFyc2VkLmdvYWwgfHwgIXBhcnNlZC5zdWJ0YXNrcyB8fCAhQXJyYXkuaXNBcnJheShwYXJzZWQuc3VidGFza3MpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBwbGFuIHN0cnVjdHVyZSBmcm9tIEFJJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIENvbnZlcnQgQUkgc3VidGFza3MgdG8gb3VyIGZvcm1hdFxuICAgICAgY29uc3Qgc3VidGFza3M6IEJyb3dzaW5nU3VidGFza1tdID0gcGFyc2VkLnN1YnRhc2tzLm1hcCgodGFzazogYW55LCBpbmRleDogbnVtYmVyKSA9PiAoe1xuICAgICAgICBpZDogdGFzay5pZCB8fCBgYWlfdGFza18ke0RhdGUubm93KCl9XyR7aW5kZXh9YCxcbiAgICAgICAgdHlwZTogdGFzay50eXBlIHx8ICdzZWFyY2gnLFxuICAgICAgICBkZXNjcmlwdGlvbjogdGFzay5kZXNjcmlwdGlvbiB8fCBgVGFzayAke2luZGV4ICsgMX1gLFxuICAgICAgICBxdWVyeTogdGFzay5xdWVyeSB8fCBvcmlnaW5hbFF1ZXJ5LFxuICAgICAgICBzdGF0dXM6ICdwZW5kaW5nJyBhcyBjb25zdCxcbiAgICAgICAgcHJpb3JpdHk6IHRhc2sucHJpb3JpdHkgfHwgNSxcbiAgICAgICAgYXR0ZW1wdHM6IDAsXG4gICAgICAgIG1heEF0dGVtcHRzOiAzLFxuICAgICAgICBzZWFyY2hUZXJtczogdGFzay5zZWFyY2hUZXJtcyB8fCBbXSxcbiAgICAgICAgZXhwZWN0ZWRJbmZvOiB0YXNrLmV4cGVjdGVkSW5mbyB8fCAnJ1xuICAgICAgfSkpO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBnb2FsOiBwYXJzZWQuZ29hbCxcbiAgICAgICAgc3VidGFza3NcbiAgICAgIH07XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKCdbU21hcnQgQnJvd3NpbmddIEZhaWxlZCB0byBwYXJzZSBBSSBwbGFuLCB1c2luZyBmYWxsYmFjazonLCBlcnJvcik7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBnb2FsOiBgRmluZCBpbmZvcm1hdGlvbiBhYm91dDogJHtvcmlnaW5hbFF1ZXJ5fWAsXG4gICAgICAgIHN1YnRhc2tzOiB0aGlzLmNyZWF0ZUZhbGxiYWNrUGxhbihvcmlnaW5hbFF1ZXJ5LCAnc2VhcmNoJylcbiAgICAgIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFN5bnRoZXNpemUgZmluYWwgcmVzdWx0IGZyb20gYWxsIGdhdGhlcmVkIGRhdGFcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgc3ludGhlc2l6ZUZpbmFsUmVzdWx0KFxuICAgIHBsYW46IEJyb3dzaW5nUGxhbixcbiAgICBicm93c2luZ0NvbmZpZzogQnJvd3NpbmdDb25maWdcbiAgKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICB0cnkge1xuICAgICAgLy8gRmluZCB0aGUgYW5hbHlzaXMgcmVzdWx0IGlmIGF2YWlsYWJsZVxuICAgICAgY29uc3QgYW5hbHlzaXNUYXNrID0gcGxhbi5zdWJ0YXNrcy5maW5kKHRhc2sgPT5cbiAgICAgICAgdGFzay50eXBlID09PSAnYW5hbHl6ZScgJiYgdGFzay5zdGF0dXMgPT09ICdjb21wbGV0ZWQnICYmIHRhc2sucmVzdWx0XG4gICAgICApO1xuXG4gICAgICBpZiAoYW5hbHlzaXNUYXNrICYmIGFuYWx5c2lzVGFzay5yZXN1bHQ/LmFuYWx5c2lzKSB7XG4gICAgICAgIHJldHVybiBhbmFseXNpc1Rhc2sucmVzdWx0LmFuYWx5c2lzO1xuICAgICAgfVxuXG4gICAgICAvLyBJZiBubyBhbmFseXNpcyB0YXNrLCBjcmVhdGUgYSBzdW1tYXJ5IGZyb20gYWxsIGNvbXBsZXRlZCB0YXNrc1xuICAgICAgY29uc3QgY29tcGxldGVkVGFza3MgPSBwbGFuLnN1YnRhc2tzLmZpbHRlcih0YXNrID0+XG4gICAgICAgIHRhc2suc3RhdHVzID09PSAnY29tcGxldGVkJyAmJiB0YXNrLnJlc3VsdFxuICAgICAgKTtcblxuICAgICAgaWYgKGNvbXBsZXRlZFRhc2tzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICByZXR1cm4gYE5vIGluZm9ybWF0aW9uIHdhcyBzdWNjZXNzZnVsbHkgZ2F0aGVyZWQgZm9yIHRoZSBxdWVyeTogXCIke3BsYW4ub3JpZ2luYWxRdWVyeX1cImA7XG4gICAgICB9XG5cbiAgICAgIC8vIENyZWF0ZSBhIGJhc2ljIHN1bW1hcnlcbiAgICAgIGxldCBzdW1tYXJ5ID0gYEJhc2VkIG9uIGJyb3dzaW5nIHJlc2VhcmNoIGZvciBcIiR7cGxhbi5vcmlnaW5hbFF1ZXJ5fVwiOlxcblxcbmA7XG5cbiAgICAgIGNvbXBsZXRlZFRhc2tzLmZvckVhY2goKHRhc2ssIGluZGV4KSA9PiB7XG4gICAgICAgIHN1bW1hcnkgKz0gYCR7aW5kZXggKyAxfS4gJHt0YXNrLmRlc2NyaXB0aW9ufTpcXG5gO1xuICAgICAgICBpZiAodGFzay5yZXN1bHQ/LnJlc3VsdHMgJiYgQXJyYXkuaXNBcnJheSh0YXNrLnJlc3VsdC5yZXN1bHRzKSkge1xuICAgICAgICAgIHRhc2sucmVzdWx0LnJlc3VsdHMuc2xpY2UoMCwgMykuZm9yRWFjaCgocmVzdWx0OiBhbnkpID0+IHtcbiAgICAgICAgICAgIHN1bW1hcnkgKz0gYCAgIOKAoiAke3Jlc3VsdC50aXRsZSB8fCAnUmVzdWx0J31cXG5gO1xuICAgICAgICAgIH0pO1xuICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiB0YXNrLnJlc3VsdCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICBzdW1tYXJ5ICs9IGAgICAke3Rhc2sucmVzdWx0LnN1YnN0cmluZygwLCAyMDApfS4uLlxcbmA7XG4gICAgICAgIH1cbiAgICAgICAgc3VtbWFyeSArPSAnXFxuJztcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gc3VtbWFyeTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdbU21hcnQgQnJvd3NpbmddIEVycm9yIHN5bnRoZXNpemluZyBmaW5hbCByZXN1bHQ6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGBSZXNlYXJjaCBjb21wbGV0ZWQgZm9yIFwiJHtwbGFuLm9yaWdpbmFsUXVlcnl9XCIgYnV0IGVuY291bnRlcmVkIGVycm9ycyBpbiBzeW50aGVzaXMuIFBsZWFzZSBjaGVjayB0aGUgaW5kaXZpZHVhbCByZXN1bHRzLmA7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIExvZyB0aGUgYnJvd3NpbmcgcGxhbiBmb3IgZGVidWdnaW5nXG4gICAqL1xuICBwcml2YXRlIGxvZ1BsYW4ocGxhbjogQnJvd3NpbmdQbGFuKTogdm9pZCB7XG4gICAgY29uc29sZS5sb2coYFtTbWFydCBCcm93c2luZ10g8J+TiyBCUk9XU0lORyBQTEFOOmApO1xuICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddIEdvYWw6ICR7cGxhbi5nb2FsfWApO1xuICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddIFN1YnRhc2tzOmApO1xuICAgIHBsYW4uc3VidGFza3MuZm9yRWFjaCgoc3VidGFzaywgaW5kZXgpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddICAgJHtpbmRleCArIDF9LiBbJHtzdWJ0YXNrLnR5cGUudG9VcHBlckNhc2UoKX1dICR7c3VidGFzay5kZXNjcmlwdGlvbn1gKTtcbiAgICAgIGNvbnNvbGUubG9nKGBbU21hcnQgQnJvd3NpbmddICAgICAgUXVlcnk6IFwiJHtzdWJ0YXNrLnF1ZXJ5fVwiYCk7XG4gICAgICBjb25zb2xlLmxvZyhgW1NtYXJ0IEJyb3dzaW5nXSAgICAgIFByaW9yaXR5OiAke3N1YnRhc2sucHJpb3JpdHl9LCBTdGF0dXM6ICR7c3VidGFzay5zdGF0dXN9YCk7XG4gICAgICBpZiAoc3VidGFzay5zZWFyY2hUZXJtcyAmJiBzdWJ0YXNrLnNlYXJjaFRlcm1zLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc29sZS5sb2coYFtTbWFydCBCcm93c2luZ10gICAgICBBbHQgdGVybXM6ICR7c3VidGFzay5zZWFyY2hUZXJtcy5qb2luKCcsICcpfWApO1xuICAgICAgfVxuICAgIH0pO1xuICB9XG59XG4iXSwibmFtZXMiOlsiQnJvd3Nlcmxlc3NTZXJ2aWNlIiwiU21hcnRCcm93c2luZ0V4ZWN1dG9yIiwiY29uc3RydWN0b3IiLCJhY3RpdmVQbGFucyIsIk1hcCIsImJyb3dzZXJsZXNzU2VydmljZSIsImdldEluc3RhbmNlIiwiaW5zdGFuY2UiLCJleGVjdXRlU21hcnRCcm93c2luZyIsInF1ZXJ5IiwiYnJvd3NpbmdDb25maWciLCJicm93c2luZ1R5cGUiLCJwcm9ncmVzc0NhbGxiYWNrIiwiY29uc29sZSIsImxvZyIsInBsYW4iLCJjcmVhdGVCcm93c2luZ1BsYW4iLCJzZXQiLCJpZCIsInN1YnRhc2tzIiwibGVuZ3RoIiwibG9nUGxhbiIsIm9uUGxhbkNyZWF0ZWQiLCJyZXN1bHQiLCJleGVjdXRlUGxhbiIsInN1Y2Nlc3MiLCJjb250ZW50IiwiZXJyb3IiLCJpc05ldHdvcmtFcnJvciIsInNob3VsZEZhbGxiYWNrIiwiZXJyb3JNZXNzYWdlIiwiRXJyb3IiLCJtZXNzYWdlIiwibmV0d29ya0Vycm9yUGF0dGVybnMiLCJzb21lIiwicGF0dGVybiIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJwbGFuSWQiLCJEYXRlIiwibm93IiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwicGxhbm5pbmdSZXN1bHQiLCJnZW5lcmF0ZVBsYW5XaXRoQUkiLCJvcmlnaW5hbFF1ZXJ5IiwiZ29hbCIsImNyZWF0ZUZhbGxiYWNrUGxhbiIsInN0YXR1cyIsInByb2dyZXNzIiwiZ2F0aGVyZWREYXRhIiwidmlzaXRlZFVybHMiLCJzZWFyY2hRdWVyaWVzIiwiY29tcGxldGVkU3VidGFza3MiLCJmYWlsZWRTdWJ0YXNrcyIsImNyZWF0ZWRBdCIsInRvSVNPU3RyaW5nIiwidXBkYXRlZEF0IiwibW9kZWwiLCJicm93c2luZ19tb2RlbHMiLCJwbGFubmluZ1Byb21wdCIsImJ1aWxkUGxhbm5pbmdQcm9tcHQiLCJhaVJlc3VsdCIsImNhbGxBSUZvclBsYW5uaW5nIiwicGFyc2VQbGFuRnJvbUFJIiwid2FybiIsImN1cnJlbnREYXRlVGltZSIsInRvTG9jYWxlU3RyaW5nIiwid2Vla2RheSIsInllYXIiLCJtb250aCIsImRheSIsImhvdXIiLCJtaW51dGUiLCJ0aW1lWm9uZU5hbWUiLCJjdXJyZW50WWVhciIsImdldEZ1bGxZZWFyIiwiY3VycmVudE1vbnRoIiwiZW5oYW5jZVF1ZXJ5V2l0aFRlbXBvcmFsIiwidGVtcG9yYWxLZXl3b3JkcyIsImhhc1RlbXBvcmFsQ29udGV4dCIsInRlc3QiLCJwcmltYXJ5IiwidGVtcG9yYWwiLCJhbHRlcm5hdGl2ZXMiLCJyZWNlbnRUZXJtcyIsImJhc2VJZCIsImVuaGFuY2VkUXVlcnkiLCJ0eXBlIiwiZGVzY3JpcHRpb24iLCJwcmlvcml0eSIsImF0dGVtcHRzIiwibWF4QXR0ZW1wdHMiLCJzZWFyY2hUZXJtcyIsImV4cGVjdGVkSW5mbyIsImRlcGVuZGVuY2llcyIsImhhc1JlbWFpbmluZ1Rhc2tzIiwibmV4dFRhc2siLCJnZXROZXh0RXhlY3V0YWJsZVRhc2siLCJjdXJyZW50U3VidGFzayIsIm9uVGFza1N0YXJ0ZWQiLCJvblN0YXR1c1VwZGF0ZSIsInRhc2tSZXN1bHQiLCJleGVjdXRlU3VidGFzayIsImRhdGEiLCJwdXNoIiwib25UYXNrQ29tcGxldGVkIiwib25UYXNrRmFpbGVkIiwiY2FsY3VsYXRlUHJvZ3Jlc3MiLCJvblByb2dyZXNzVXBkYXRlIiwibG9nUHJvZ3Jlc3MiLCJmaW5hbFJlc3VsdCIsInN5bnRoZXNpemVGaW5hbFJlc3VsdCIsIm9uUGxhbkNvbXBsZXRlZCIsInRhc2siLCJleGVjdXRhYmxlVGFza3MiLCJmaWx0ZXIiLCJldmVyeSIsImRlcElkIiwic29ydCIsImEiLCJiIiwic3VidGFzayIsImV4ZWN1dGVTZWFyY2hTdWJ0YXNrIiwiZXhlY3V0ZU5hdmlnYXRlU3VidGFzayIsImV4ZWN1dGVFeHRyYWN0U3VidGFzayIsImV4ZWN1dGVBbmFseXplU3VidGFzayIsImxhc3RFcnJvciIsInNlYXJjaFRlcm0iLCJzZWFyY2hBbmRFeHRyYWN0VW5ibG9ja2VkIiwicmVzdWx0cyIsImZvckVhY2giLCJpdGVtIiwibGluayIsInVybFBhdHRlcm4iLCJuYXZpZ2F0ZUFuZEV4dHJhY3QiLCJzZWFyY2hRdWVyeSIsInJlcGxhY2UiLCJ0cmltIiwibWFwIiwiYW5hbHlzaXNQcm9tcHQiLCJidWlsZEFuYWx5c2lzUHJvbXB0IiwiY2FsbEFJRm9yQW5hbHlzaXMiLCJhbmFseXNpcyIsInNvdXJjZURhdGEiLCJ0b3RhbFRhc2tzIiwiY29tcGxldGVkVGFza3MiLCJyb3VuZCIsImNvbXBsZXRlZCIsImZhaWxlZCIsInRvdGFsIiwicmVtYWluaW5nIiwiZGF0YUNvbnRleHQiLCJpbmRleCIsIkpTT04iLCJzdHJpbmdpZnkiLCJqb2luIiwicHJvbXB0IiwiZWZmZWN0aXZlTW9kZWxJZCIsImdldEVmZmVjdGl2ZU1vZGVsSWQiLCJtZXNzYWdlcyIsInJvbGUiLCJhcGlVcmwiLCJoZWFkZXJzIiwiYm9keSIsInByb3ZpZGVyIiwiYXBpX2tleSIsInRlbXBlcmF0dXJlIiwibWF4X3Rva2VucyIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJzaWduYWwiLCJBYm9ydFNpZ25hbCIsInRpbWVvdXQiLCJvayIsImVycm9yVGV4dCIsInRleHQiLCJqc29uIiwiY2hvaWNlcyIsInBhcnRzIiwic3BsaXQiLCJhaVJlc3BvbnNlIiwianNvbk1hdGNoIiwibWF0Y2giLCJwYXJzZWQiLCJwYXJzZSIsIkFycmF5IiwiaXNBcnJheSIsImFuYWx5c2lzVGFzayIsImZpbmQiLCJzdW1tYXJ5Iiwic2xpY2UiLCJ0aXRsZSIsInN1YnN0cmluZyIsInRvVXBwZXJDYXNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts\n");

/***/ })

};
;