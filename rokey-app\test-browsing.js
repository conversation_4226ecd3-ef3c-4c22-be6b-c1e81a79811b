// Simple test script to debug browsing functionality
const testBrowsing = async () => {
  try {
    console.log('Testing browsing functionality...');
    
    const response = await fetch('http://localhost:3000/api/debug/browsing', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'AI news 2024',
        searchEngine: 'google'
      }),
    });

    const data = await response.json();
    console.log('Response:', JSON.stringify(data, null, 2));

  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Also test the stats endpoint
const testStats = async () => {
  try {
    console.log('Testing browsing stats...');
    
    const response = await fetch('http://localhost:3000/api/debug/browsing');
    const data = await response.json();
    console.log('Stats:', JSON.stringify(data, null, 2));

  } catch (error) {
    console.error('Stats test failed:', error);
  }
};

// Run tests
testStats().then(() => testBrowsing());
