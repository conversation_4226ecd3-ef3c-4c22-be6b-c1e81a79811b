/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/debug/browsing/route";
exports.ids = ["app/api/debug/browsing/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdebug%2Fbrowsing%2Froute&page=%2Fapi%2Fdebug%2Fbrowsing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug%2Fbrowsing%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdebug%2Fbrowsing%2Froute&page=%2Fapi%2Fdebug%2Fbrowsing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug%2Fbrowsing%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_debug_browsing_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/debug/browsing/route.ts */ \"(rsc)/./src/app/api/debug/browsing/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/debug/browsing/route\",\n        pathname: \"/api/debug/browsing\",\n        filename: \"route\",\n        bundlePath: \"app/api/debug/browsing/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\debug\\\\browsing\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_debug_browsing_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdebug%2Fbrowsing%2Froute&page=%2Fapi%2Fdebug%2Fbrowsing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug%2Fbrowsing%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/debug/browsing/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/debug/browsing/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n\n\nasync function POST(request) {\n    try {\n        const { query, searchEngine = 'google' } = await request.json();\n        if (!query) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Query is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`[Debug Browsing] Testing search for: \"${query}\"`);\n        const browserless = _lib_browserless__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getInstance();\n        const result = await browserless.searchAndExtract(query, searchEngine);\n        console.log(`[Debug Browsing] Search result:`, JSON.stringify(result, null, 2));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            query,\n            searchEngine,\n            result,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('[Debug Browsing] Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Browsing test failed',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    try {\n        const browserless = _lib_browserless__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getInstance();\n        const stats = browserless.getStats();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'operational',\n            service: 'debug-browsing',\n            stats,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('[Debug Browsing] Status check error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'error',\n            service: 'debug-browsing',\n            error: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/debug/browsing/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browserless.ts":
/*!********************************!*\
  !*** ./src/lib/browserless.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browserless.io API service with key rotation\n// Similar to Jina API key rotation system\nclass BrowserlessService {\n    constructor(){\n        this.apiKeys = [];\n        this.currentKeyIndex = 0;\n        this.keyUsageCount = new Map();\n        this.keyErrors = new Map();\n        this.MAX_RETRIES = 3;\n        this.ERROR_THRESHOLD = 5;\n        this.ENDPOINT = 'https://production-sfo.browserless.io';\n        this.initializeKeys();\n        this.testConnectivity();\n    }\n    static getInstance() {\n        if (!BrowserlessService.instance) {\n            BrowserlessService.instance = new BrowserlessService();\n        }\n        return BrowserlessService.instance;\n    }\n    initializeKeys() {\n        // Load all Browserless API keys from environment (similar to Jina pattern)\n        this.apiKeys = [\n            process.env.BROWSERLESS_API_KEY,\n            process.env.BROWSERLESS_API_KEY_2,\n            process.env.BROWSERLESS_API_KEY_3,\n            process.env.BROWSERLESS_API_KEY_4,\n            process.env.BROWSERLESS_API_KEY_5,\n            process.env.BROWSERLESS_API_KEY_6,\n            process.env.BROWSERLESS_API_KEY_7,\n            process.env.BROWSERLESS_API_KEY_8,\n            process.env.BROWSERLESS_API_KEY_9,\n            process.env.BROWSERLESS_API_KEY_10\n        ].filter(Boolean);\n        if (this.apiKeys.length === 0) {\n            console.error('No Browserless API keys found in environment variables');\n            return;\n        }\n        console.log(`[Browserless] Initialized with ${this.apiKeys.length} API keys`);\n        // Initialize usage tracking\n        this.apiKeys.forEach((key)=>{\n            this.keyUsageCount.set(key, 0);\n            this.keyErrors.set(key, 0);\n        });\n    }\n    /**\n   * Test connectivity to Browserless service\n   */ async testConnectivity() {\n        if (this.apiKeys.length === 0) {\n            console.warn('[Browserless] No API keys available for connectivity test');\n            return;\n        }\n        try {\n            const testKey = this.apiKeys[0];\n            const testUrl = `${this.ENDPOINT}/function?token=${testKey}`;\n            // Simple connectivity test with proper function format\n            const testCode = `\n        export default async function ({ page }) {\n          return {\n            status: \"connectivity-test-success\",\n            timestamp: new Date().toISOString()\n          };\n        }\n      `;\n            const response = await fetch(testUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/javascript'\n                },\n                body: testCode,\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const result = await response.text();\n                console.log('[Browserless] ✅ Connectivity test successful:', result);\n            } else {\n                const errorText = await response.text();\n                console.warn(`[Browserless] ⚠️ Connectivity test failed with status: ${response.status}`);\n                console.warn(`[Browserless] Error details: ${errorText}`);\n                console.warn(`[Browserless] Using key: ${testKey.substring(0, 8)}...`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.warn(`[Browserless] ⚠️ Connectivity test failed: ${errorMessage}`);\n            // Check for specific network errors\n            if (errorMessage.includes('ENOTFOUND')) {\n                console.error('[Browserless] 🌐 DNS resolution failed - check if chrome.browserless.io is accessible');\n            } else if (errorMessage.includes('ECONNRESET')) {\n                console.error('[Browserless] 🔌 Connection reset - possible network or service issue');\n            }\n        }\n    }\n    getNextApiKey() {\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Browserless API keys available');\n        }\n        // Find the key with lowest usage and errors\n        let bestKey = this.apiKeys[0];\n        let bestScore = this.calculateKeyScore(bestKey);\n        for (const key of this.apiKeys){\n            const score = this.calculateKeyScore(key);\n            if (score < bestScore) {\n                bestKey = key;\n                bestScore = score;\n            }\n        }\n        return bestKey;\n    }\n    calculateKeyScore(key) {\n        const usage = this.keyUsageCount.get(key) || 0;\n        const errors = this.keyErrors.get(key) || 0;\n        // Higher score = worse key (more usage + more errors)\n        return usage + errors * 10;\n    }\n    incrementKeyUsage(key) {\n        const currentUsage = this.keyUsageCount.get(key) || 0;\n        this.keyUsageCount.set(key, currentUsage + 1);\n    }\n    incrementKeyError(key) {\n        const currentErrors = this.keyErrors.get(key) || 0;\n        this.keyErrors.set(key, currentErrors + 1);\n    }\n    isKeyHealthy(key) {\n        const errors = this.keyErrors.get(key) || 0;\n        return errors < this.ERROR_THRESHOLD;\n    }\n    getHealthyKeys() {\n        return this.apiKeys.filter((key)=>this.isKeyHealthy(key));\n    }\n    async executeFunction(code, context, config) {\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            // Reset error counts if all keys are unhealthy\n            this.keyErrors.clear();\n            this.apiKeys.forEach((key)=>this.keyErrors.set(key, 0));\n            console.log('All Browserless keys were unhealthy, resetting error counts');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const response = await this.makeRequest(apiKey, code, context, config);\n                // Success - return the response\n                return response;\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless attempt ${attempt + 1} failed:`, error);\n                // If it's a rate limit or quota error, mark the key as having an error\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless API attempts failed');\n    }\n    async makeRequest(apiKey, code, context, config) {\n        const url = `${this.ENDPOINT}/function?token=${apiKey}`;\n        const requestBody = context ? {\n            code,\n            context\n        } : code;\n        const headers = {\n            'Content-Type': context ? 'application/json' : 'application/javascript',\n            'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'\n        };\n        const response = await fetch(url, {\n            method: 'POST',\n            headers,\n            body: context ? JSON.stringify(requestBody) : code,\n            signal: AbortSignal.timeout(config?.timeout || 30000)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Browserless API error: ${response.status} - ${errorText}`);\n        }\n        const result = await response.json();\n        return result;\n    }\n    isRateLimitError(error) {\n        const message = error.message.toLowerCase();\n        return message.includes('rate limit') || message.includes('quota') || message.includes('429') || message.includes('too many requests');\n    }\n    // Convenience methods for common browser tasks\n    async navigateAndExtract(url, selector) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n        \n        const title = await page.title();\n        const content = ${selector ? `await page.$eval(\"${selector}\", el => el.textContent || el.innerText)` : 'await page.evaluate(() => document.body.innerText)'};\n        \n        return {\n          data: {\n            url: \"${url}\",\n            title,\n            content: content?.trim() || \"\"\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async searchAndExtract(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const code = `\n      export default async function ({ page }) {\n        // Set a realistic user agent and headers\n        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');\n        await page.setExtraHTTPHeaders({\n          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n          'Accept-Language': 'en-US,en;q=0.5',\n          'Accept-Encoding': 'gzip, deflate',\n          'DNT': '1',\n          'Connection': 'keep-alive',\n          'Upgrade-Insecure-Requests': '1',\n        });\n\n        await page.goto(\"${searchUrl}\", { waitUntil: 'networkidle0' });\n\n        // Wait for search results to load with multiple fallback selectors\n        let resultsLoaded = false;\n        const googleSelectors = ['[data-ved]', 'h3', '.g h3', '.LC20lb', '.DKV0Md', '#search h3'];\n        const bingSelectors = ['.b_algo', '.b_algo h2', 'h2 a'];\n\n        const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n\n        for (const selector of selectorsToTry) {\n          try {\n            await page.waitForSelector(selector, { timeout: 3000 });\n            resultsLoaded = true;\n            console.log('Found results with selector:', selector);\n            break;\n          } catch (e) {\n            console.log('Selector failed:', selector);\n            continue;\n          }\n        }\n\n        if (!resultsLoaded) {\n          // Give it one more chance with a longer timeout on the most common selector\n          try {\n            await page.waitForSelector('${searchEngine === 'google' ? 'h3' : '.b_algo'}', { timeout: 5000 });\n          } catch (e) {\n            console.log('All selectors failed, proceeding anyway...');\n          }\n        }\n\n        const results = await page.evaluate(() => {\n          console.log('Starting search results extraction...');\n\n          // Try multiple selectors for extracting results\n          const googleSelectors = [\n            '[data-ved] h3',\n            'h3',\n            '.g h3',\n            '.LC20lb',\n            '.DKV0Md',\n            '#search h3',\n            '.yuRUbf h3',\n            'a h3',\n            '[role=\"heading\"]'\n          ];\n          const bingSelectors = ['.b_algo h2', '.b_algo h2 a', 'h2 a'];\n\n          const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n          let elements = [];\n          let usedSelector = '';\n\n          // Debug: Log page content\n          console.log('Page title:', document.title);\n          console.log('Page URL:', window.location.href);\n          console.log('Page body preview:', document.body.innerText.substring(0, 500));\n\n          for (const selector of selectorsToTry) {\n            elements = document.querySelectorAll(selector);\n            console.log('Trying selector:', selector, 'found:', elements.length, 'elements');\n            if (elements.length > 0) {\n              usedSelector = selector;\n              console.log('Found', elements.length, 'results with selector:', selector);\n              break;\n            }\n          }\n\n          // If no results found, try a more generic approach\n          if (elements.length === 0) {\n            console.log('No results with specific selectors, trying generic approach...');\n            // Try to find any links that look like search results\n            const allLinks = document.querySelectorAll('a[href*=\"/url?\"]');\n            console.log('Found', allLinks.length, 'Google result links');\n\n            if (allLinks.length > 0) {\n              elements = Array.from(allLinks).map(link => {\n                const h3 = link.querySelector('h3');\n                return h3 || link;\n              }).filter(el => el && el.textContent?.trim());\n              usedSelector = 'a[href*=\"/url?\"] h3 (fallback)';\n              console.log('Using fallback approach, found', elements.length, 'elements');\n            }\n          }\n\n          const extractedResults = Array.from(elements).slice(0, 5).map(el => {\n            const title = el.textContent?.trim() || '';\n            let link = '';\n\n            // Try to get the link\n            if (el.href) {\n              link = el.href;\n            } else {\n              const closestLink = el.closest('a');\n              if (closestLink) {\n                link = closestLink.href;\n              }\n            }\n\n            return { title, link };\n          }).filter(item => item.title && item.link);\n\n          console.log('Final results:', extractedResults.length, 'items using selector:', usedSelector);\n\n          return {\n            results: extractedResults,\n            debug: {\n              pageTitle: document.title,\n              pageUrl: window.location.href,\n              totalElements: elements.length,\n              usedSelector: usedSelector || 'none',\n              extractedCount: extractedResults.length\n            }\n          };\n        });\n\n        return {\n          data: {\n            query: \"${query}\",\n            searchEngine: \"${searchEngine}\",\n            results: results.results,\n            timestamp: new Date().toISOString(),\n            debug: results.debug\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async takeScreenshot(url, options) {\n        const fullPage = options?.fullPage ?? false;\n        const selector = options?.selector || '';\n        const quality = options?.quality || 80;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        let screenshot;\n        if (\"${selector}\") {\n          // Screenshot specific element\n          const element = await page.waitForSelector(\"${selector}\", { timeout: 10000 });\n          screenshot = await element.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n        } else {\n          // Screenshot full page or viewport\n          screenshot = await page.screenshot({\n            encoding: 'base64',\n            fullPage: ${fullPage},\n            type: 'png',\n            quality: ${quality}\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            screenshot: screenshot,\n            selector: \"${selector}\",\n            fullPage: ${fullPage},\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced form filling with intelligent field detection\n   */ async fillForm(url, formData, options) {\n        const submitAfterFill = options?.submitAfterFill ?? false;\n        const waitForNavigation = options?.waitForNavigation ?? false;\n        const formSelector = options?.formSelector || 'form';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const formData = ${JSON.stringify(formData)};\n        const results = [];\n\n        // Wait for form to be present\n        await page.waitForSelector(\"${formSelector}\", { timeout: 10000 });\n\n        // Fill each field intelligently\n        for (const [fieldName, value] of Object.entries(formData)) {\n          try {\n            // Try multiple selector strategies\n            const selectors = [\n              \\`input[name=\"\\${fieldName}\"]\\`,\n              \\`input[id=\"\\${fieldName}\"]\\`,\n              \\`textarea[name=\"\\${fieldName}\"]\\`,\n              \\`select[name=\"\\${fieldName}\"]\\`,\n              \\`input[placeholder*=\"\\${fieldName}\"]\\`,\n              \\`input[aria-label*=\"\\${fieldName}\"]\\`,\n              \\`[data-testid=\"\\${fieldName}\"]\\`\n            ];\n\n            let filled = false;\n            for (const selector of selectors) {\n              const elements = await page.$$(selector);\n              if (elements.length > 0) {\n                const element = elements[0];\n                const tagName = await element.evaluate(el => el.tagName.toLowerCase());\n\n                if (tagName === 'select') {\n                  await element.selectOption(value.toString());\n                } else if (tagName === 'input') {\n                  const inputType = await element.getAttribute('type');\n                  if (inputType === 'checkbox' || inputType === 'radio') {\n                    if (value) await element.check();\n                  } else {\n                    await element.fill(value.toString());\n                  }\n                } else {\n                  await element.fill(value.toString());\n                }\n\n                results.push({\n                  field: fieldName,\n                  selector: selector,\n                  value: value,\n                  success: true\n                });\n                filled = true;\n                break;\n              }\n            }\n\n            if (!filled) {\n              results.push({\n                field: fieldName,\n                value: value,\n                success: false,\n                error: 'Field not found'\n              });\n            }\n          } catch (error) {\n            results.push({\n              field: fieldName,\n              value: value,\n              success: false,\n              error: error.message\n            });\n          }\n        }\n\n        let submitResult = null;\n        if (${submitAfterFill}) {\n          try {\n            const submitButton = await page.$('input[type=\"submit\"], button[type=\"submit\"], button:has-text(\"Submit\")');\n            if (submitButton) {\n              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}\n              submitResult = { success: true, message: 'Form submitted successfully' };\n            } else {\n              submitResult = { success: false, error: 'Submit button not found' };\n            }\n          } catch (error) {\n            submitResult = { success: false, error: error.message };\n          }\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            formFillResults: results,\n            submitResult: submitResult,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * CAPTCHA solving with multiple strategies\n   */ async solveCaptcha(url, captchaType = 'recaptcha') {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const captchaType = \"${captchaType}\";\n        let result = { success: false, type: captchaType };\n\n        try {\n          if (captchaType === 'recaptcha') {\n            // Look for reCAPTCHA\n            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');\n            if (recaptcha) {\n              // For now, we'll detect and report the presence\n              // In production, integrate with 2captcha or similar service\n              const sitekey = await recaptcha.getAttribute('data-sitekey');\n              result = {\n                success: false,\n                type: 'recaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'reCAPTCHA detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'hcaptcha') {\n            // Look for hCaptcha\n            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');\n            if (hcaptcha) {\n              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');\n              result = {\n                success: false,\n                type: 'hcaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'hCaptcha detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'text') {\n            // Look for text-based CAPTCHA\n            const textCaptcha = await page.$('img[src*=\"captcha\"], img[alt*=\"captcha\"], .captcha-image');\n            if (textCaptcha) {\n              result = {\n                success: false,\n                type: 'text',\n                detected: true,\n                message: 'Text CAPTCHA detected but solving not implemented yet'\n              };\n            }\n          }\n\n          // If no CAPTCHA detected\n          if (!result.detected) {\n            result = {\n              success: true,\n              type: captchaType,\n              detected: false,\n              message: 'No CAPTCHA detected on page'\n            };\n          }\n        } catch (error) {\n          result = {\n            success: false,\n            type: captchaType,\n            error: error.message\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            captchaResult: result,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Execute custom JavaScript with advanced capabilities\n   */ async executeAdvancedScript(url, script, options) {\n        const waitForSelector = options?.waitForSelector || '';\n        const timeout = options?.timeout || 30000;\n        const returnType = options?.returnType || 'json';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        ${waitForSelector ? `await page.waitForSelector(\"${waitForSelector}\", { timeout: ${timeout} });` : ''}\n\n        // Execute custom script\n        const scriptResult = await page.evaluate(() => {\n          ${script}\n        });\n\n        let finalResult = scriptResult;\n\n        if (\"${returnType}\" === 'screenshot') {\n          const screenshot = await page.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n          finalResult = {\n            scriptResult: scriptResult,\n            screenshot: screenshot\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            result: finalResult,\n            returnType: \"${returnType}\",\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Smart content extraction with multiple strategies\n   */ async smartExtract(url, extractionGoals) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const goals = ${JSON.stringify(extractionGoals)};\n        const results = {};\n\n        // Common extraction patterns\n        const extractors = {\n          prices: () => {\n            const priceSelectors = [\n              '[class*=\"price\"]', '[id*=\"price\"]', '.cost', '.amount',\n              '[data-testid*=\"price\"]', '.currency', '[class*=\"dollar\"]'\n            ];\n            const prices = [];\n            priceSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const text = el.textContent?.trim();\n                if (text && /[$£€¥₹]|\\\\d+\\\\.\\\\d{2}/.test(text)) {\n                  prices.push({\n                    text: text,\n                    selector: selector,\n                    element: el.tagName\n                  });\n                }\n              });\n            });\n            return prices;\n          },\n\n          contact: () => {\n            const contactSelectors = [\n              '[href^=\"mailto:\"]', '[href^=\"tel:\"]', '.contact', '.email', '.phone'\n            ];\n            const contacts = [];\n            contactSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                contacts.push({\n                  text: el.textContent?.trim(),\n                  href: el.getAttribute('href'),\n                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'\n                });\n              });\n            });\n            return contacts;\n          },\n\n          products: () => {\n            const productSelectors = [\n              '.product', '[class*=\"product\"]', '.item', '[data-testid*=\"product\"]'\n            ];\n            const products = [];\n            productSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const title = el.querySelector('h1, h2, h3, .title, [class*=\"title\"]')?.textContent?.trim();\n                const price = el.querySelector('[class*=\"price\"], .cost')?.textContent?.trim();\n                const image = el.querySelector('img')?.src;\n                if (title) {\n                  products.push({ title, price, image });\n                }\n              });\n            });\n            return products;\n          },\n\n          text: () => {\n            // Extract main content\n            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];\n            let content = '';\n            for (const selector of contentSelectors) {\n              const el = document.querySelector(selector);\n              if (el) {\n                content = el.textContent?.trim() || '';\n                break;\n              }\n            }\n            if (!content) {\n              content = document.body.textContent?.trim() || '';\n            }\n            return content.substring(0, 5000); // Limit to 5000 chars\n          },\n\n          links: () => {\n            const links = [];\n            document.querySelectorAll('a[href]').forEach(el => {\n              const href = el.getAttribute('href');\n              const text = el.textContent?.trim();\n              if (href && text && !href.startsWith('#')) {\n                links.push({\n                  url: new URL(href, window.location.href).href,\n                  text: text\n                });\n              }\n            });\n            return links.slice(0, 50); // Limit to 50 links\n          }\n        };\n\n        // Execute extractors based on goals\n        goals.forEach(goal => {\n          const goalLower = goal.toLowerCase();\n          if (goalLower.includes('price') || goalLower.includes('cost')) {\n            results.prices = extractors.prices();\n          }\n          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {\n            results.contact = extractors.contact();\n          }\n          if (goalLower.includes('product') || goalLower.includes('item')) {\n            results.products = extractors.products();\n          }\n          if (goalLower.includes('text') || goalLower.includes('content')) {\n            results.text = extractors.text();\n          }\n          if (goalLower.includes('link') || goalLower.includes('url')) {\n            results.links = extractors.links();\n          }\n        });\n\n        // If no specific goals, extract everything\n        if (goals.length === 0) {\n          Object.keys(extractors).forEach(key => {\n            results[key] = extractors[key]();\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            extractionGoals: goals,\n            results: results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    // Get service statistics\n    getStats() {\n        return {\n            totalKeys: this.apiKeys.length,\n            healthyKeys: this.getHealthyKeys().length,\n            keyUsage: Object.fromEntries(this.keyUsageCount),\n            keyErrors: Object.fromEntries(this.keyErrors)\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowserlessService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browserless.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdebug%2Fbrowsing%2Froute&page=%2Fapi%2Fdebug%2Fbrowsing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug%2Fbrowsing%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();